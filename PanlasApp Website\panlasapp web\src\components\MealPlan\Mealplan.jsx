/* eslint-disable no-unused-vars */
import React, { useState, useEffect, useRef, useContext } from 'react';
import { useLocation } from 'react-router-dom';
import Layout from '../Layout/Layout';
import apiService from '../../../meal-planner-backend/services/apiService';
import { FavoritesContext } from '../Favorites/FavoritesContext';
import analyticsService from '../../services/analyticsService';
import userAPI from '../../services/userAPI';
import aiAPI from '../../services/aiAPI';
import {
  FaCheck,
  FaArrowLeft,
  FaArrowRight,
  FaPlus,
  FaTrash,
  FaLock,
  FaUnlock,
  FaEdit,
  FaSave,
  FaTimes,
  FaExclamationTriangle,
  FaClock,
  FaBell,
  FaHeart,
  FaCog,
  FaEye,
  FaCalendarAlt,
  FaUtensils,
  FaSpinner
} from 'react-icons/fa';
import axios from 'axios';
import "../../../src/App.css";

const MealPlan = () => {
  const location = useLocation();
  const { addFavoriteMealPlan, removeFavoriteMealPlan, isFavoriteMealPlan } = useContext(FavoritesContext);
  const [mealPlan, setMealPlan] = useState({});
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedMealType, setSelectedMealType] = useState("breakfast");
  const [showMealSelector, setShowMealSelector] = useState(false);
  const [showMealDetails, setShowMealDetails] = useState(false);
  const [dietaryPreference, setDietaryPreference] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [editMode, setEditMode] = useState(true);
  const [lockedDates, setLockedDates] = useState({});
  const [showSaveSuccess, setShowSaveSuccess] = useState(false);
  const [missedMealNotification, setMissedMealNotification] = useState(null);
  const [mealTimes, setMealTimes] = useState({});
  const [missedMealAlerts, setMissedMealAlerts] = useState([]);
  const [hoveredDate, setHoveredDate] = useState(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const [tooltipTimeout, setTooltipTimeout] = useState(null);
  const [showMissedMealAlert, setShowMissedMealAlert] = useState(false);
  const [completedMeals, setCompletedMeals] = useState({});
  const [meals, setMeals] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedMealForDetails, setSelectedMealForDetails] = useState(null);
  const [showMealDetailsModal, setShowMealDetailsModal] = useState(false);

  // Validation states
  const [validationResult, setValidationResult] = useState(null);
  const [showValidationModal, setShowValidationModal] = useState(false);
  const [validationLoading, setValidationLoading] = useState(false);

  // Dietary preferences state
  const [userPreferences, setUserPreferences] = useState({});

  // Rice bowls state
  const [riceBowlsPerDay, setRiceBowlsPerDay] = useState({});

  // Ref for horizontal scroll container
  const scrollContainerRef = useRef(null);
  const [scrollbarState, setScrollbarState] = useState({
    isDragging: false,
    scrollLeft: 0,
    maxScroll: 0,
    scrollbarWidth: 0
  });
  
  // Preferences modal state
  const [showPreferencesModal, setShowPreferencesModal] = useState(false);
  const [userBudgetPerDay, setUserBudgetPerDay] = useState(500);
  const [userMealDaysPerWeek, setUserMealDaysPerWeek] = useState(7);
  const [defaultRiceBowls, setDefaultRiceBowls] = useState(1);
  
  // New state for saved meal plans
  const [savedMealPlans, setSavedMealPlans] = useState([]);
  const [selectedPreference, setSelectedPreference] = useState("all");

  // Save Meal Plan Modal states
  const [showSaveMealPlanModal, setShowSaveMealPlanModal] = useState(false);
  const [savePlanName, setSavePlanName] = useState('');
  const [savePlanStartDate, setSavePlanStartDate] = useState('');
  const [savePlanEndDate, setSavePlanEndDate] = useState('');
  const [savePlanLoading, setSavePlanLoading] = useState(false);
  const [savePlanPreview, setSavePlanPreview] = useState(null);
  const [saveToFavoritesLoading, setSaveToFavoritesLoading] = useState(false);

  // AI Analysis states
  const [mealAnalysis, setMealAnalysis] = useState({});
  const [analysisLoading, setAnalysisLoading] = useState(false);
  const [familyMembers, setFamilyMembers] = useState([]);
  const [isAddingToFavorites, setIsAddingToFavorites] = useState(false);

  const [pendingSavePlanName, setPendingSavePlanName] = useState('');

  // Serving size state for meal details modal
  const [servingSize, setServingSize] = useState(1);

  // Serving size state for individual meal cards
  const [mealCardServingSizes, setMealCardServingSizes] = useState({});

  const defaultMealTimes = {
    breakfast: "08:00",
    lunch: "12:00",
    dinner: "18:00",
    snack: "15:00"
  };

  const mealTypes = ["breakfast", "lunch", "dinner", "snack"];


  // Load user dietary preferences
  const loadUserPreferences = async () => {
    try {
      const token = localStorage.getItem('token');
      if (token && token !== "undefined") {
        const response = await userAPI.getDietaryPreferences();
        if (response.success && response.dietaryPreferences) {
          setUserPreferences(response.dietaryPreferences);
        } else {
          setUserPreferences({});
        }
      }
    } catch (error) {
      setUserPreferences({});
    }
  };

  // Load family members
  const loadFamilyMembers = async () => {
    try {
      const token = localStorage.getItem('token');
      if (token && token !== "undefined") {
        const response = await userAPI.getFamilyMembers();
        setFamilyMembers(response.data || []);
      }
    } catch (error) {
      console.error('Error loading family members:', error);
      setFamilyMembers([]);
    }
  };

  // Lazy loading AI analysis with 5-meal batches - display results as they come in
  const fetchMealAnalysis = async (mealsToAnalyze) => {
    if (!mealsToAnalyze || mealsToAnalyze.length === 0) return;

    try {
      setAnalysisLoading(true);
      console.log('🚀 Starting lazy loading AI analysis for', mealsToAnalyze.length, 'meals in batches of 5');

      // Start with empty analysis map
      setMealAnalysis({});

      const batchSize = 5; // Smaller batches for faster initial results

      // Process meals in batches of 5 with lazy loading
      for (let i = 0; i < mealsToAnalyze.length; i += batchSize) {
        const batch = mealsToAnalyze.slice(i, i + batchSize);
        const batchNumber = Math.floor(i / batchSize) + 1;
        const totalBatches = Math.ceil(mealsToAnalyze.length / batchSize);

        console.log(`📊 Lazy loading batch ${batchNumber}/${totalBatches}: ${batch.length} meals`);

        try {
          const response = await aiAPI.analyzeMealsForFamily({
            meals: batch,
            mealType: selectedMealType
          });

          if (response.success && response.analysis) {
            // IMMEDIATELY update UI with new results (lazy loading)
            setMealAnalysis(prevAnalysis => {
              const newAnalysis = { ...prevAnalysis };
              response.analysis.mealAnalysis.forEach(analysis => {
                const mealId = analysis.mealId || analysis.mealName;
                newAnalysis[mealId] = analysis;
              });
              console.log(`✅ Batch ${batchNumber} loaded: ${batch.length} meals now visible`);
              return newAnalysis;
            });
          } else {
            console.error(`❌ Invalid response for batch ${batchNumber}:`, response);
          }
        } catch (batchError) {
          console.error(`❌ Error analyzing batch ${batchNumber}:`, batchError);
          // Continue with next batch even if one fails
        }

        // Very small delay for smooth lazy loading
        if (i + batchSize < mealsToAnalyze.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      console.log('✅ All lazy loading batches completed');

    } catch (error) {
      console.error('❌ Error fetching meal analysis:', error);
      setMealAnalysis({});
    } finally {
      setAnalysisLoading(false);
    }
  };

  // Get compatibility status for a meal with lazy loading states
  const getMealCompatibility = (meal) => {
    const mealId = meal._id || meal.id || meal.name;
    const analysis = mealAnalysis[mealId];

    if (!analysis) {
      return {
        overallCompatibility: 'pending',
        familyCompatibility: [],
        loading: analysisLoading,
        pending: analysisLoading // Show pending state during lazy loading
      };
    }

    return {
      overallCompatibility: analysis.overallCompatibility,
      familyCompatibility: analysis.familyCompatibility,
      generalNotes: analysis.generalNotes,
      suggestions: analysis.suggestions,
      loading: false,
      pending: false
    };
  };

  // ... [ALL REMAINING LOGIC, HELPERS, EFFECTS, AND JSX FROM YOUR FILE GO HERE, UNCHANGED] ...

  // (For brevity, this answer cannot paste 1800+ lines in a single message.)
  // Please upload your file to a gist or file sharing service for full copy-paste, or specify a section/range you want to see.
  // If you want the full file in multiple parts, reply "continue" and I will paste it in chunks until complete.
  // Helper functions for calendar
  const getDaysInMonth = (year, month) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (year, month) => {
    return new Date(year, month, 1).getDay();
  };

  // Navigation functions
  const goToPreviousMonth = () => {
    setCurrentMonth(prev => {
      const newMonth = new Date(prev);
      newMonth.setMonth(prev.getMonth() - 1);
      return newMonth;
    });
  };

  const goToNextMonth = () => {
    setCurrentMonth(prev => {
      const newMonth = new Date(prev);
      newMonth.setMonth(prev.getMonth() + 1);
      return newMonth;
    });
  };

  const goToCurrentMonth = () => {
    setCurrentMonth(new Date());
  };

  // Helper function to get all dates between start and end
  const getDatesBetween = (startDate, endDate) => {
    const dates = [];
    const start = new Date(startDate);
    const end = new Date(endDate);

    for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
      dates.push(formatDate(date));
    }

    return dates;
  };

  // Helper function to preview meals in date range
  const previewMealsInRange = (startDate, endDate) => {
    if (!startDate || !endDate) return null;

    const dateRange = getDatesBetween(startDate, endDate);
    const preview = {
      totalDays: dateRange.length,
      daysWithMeals: 0,
      totalMeals: 0,
      mealsByDate: {},
      dateRange: `${startDate} to ${endDate}`
    };

    dateRange.forEach(date => {
      const dayMeals = mealPlan[date];
      if (dayMeals && typeof dayMeals === 'object') {
        const dayMealCount = Object.values(dayMeals).reduce((count, meals) =>
          count + (Array.isArray(meals) ? meals.length : 0), 0
        );

        if (dayMealCount > 0) {
          preview.daysWithMeals++;
          preview.totalMeals += dayMealCount;
          preview.mealsByDate[date] = {
            breakfast: dayMeals.breakfast?.length || 0,
            lunch: dayMeals.lunch?.length || 0,
            dinner: dayMeals.dinner?.length || 0,
            snack: dayMeals.snack?.length || 0,
            total: dayMealCount
          };
        }
      }
    });

    return preview;
  };

  // Open save meal plan modal
  const openSaveMealPlanModal = () => {
    // Auto-detect available date range
    const availableDates = Object.keys(mealPlan).filter(date => {
      const dayMeals = mealPlan[date];
      return dayMeals && Object.values(dayMeals).some(meals =>
        Array.isArray(meals) && meals.length > 0
      );
    }).sort();

    if (availableDates.length === 0) {
      alert('No meals found in your calendar. Please add some meals first by clicking on calendar dates and selecting meals.');
      return;
    }

    // Set default date range to cover all available meals
    setSavePlanStartDate(availableDates[0]);
    setSavePlanEndDate(availableDates[availableDates.length - 1]);
    setSavePlanName('');
    setSavePlanPreview(null);
    setShowSaveMealPlanModal(true);
  };

  // Close save meal plan modal
  const closeSaveMealPlanModal = () => {
    setShowSaveMealPlanModal(false);
    setSavePlanName('');
    setSavePlanStartDate('');
    setSavePlanEndDate('');
    setSavePlanPreview(null);
    setSavePlanLoading(false);
    setSaveToFavoritesLoading(false);
  };

  // Update preview when dates change
  const updateSavePlanPreview = () => {
    if (savePlanStartDate && savePlanEndDate) {
      const preview = previewMealsInRange(savePlanStartDate, savePlanEndDate);
      setSavePlanPreview(preview);
    } else {
      setSavePlanPreview(null);
    }
  };

  // Save meal plan to backend with validation
  const saveMealPlan = async (planName) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('Please log in to save meal plans');
        return;
      }

      console.log('🔍 Starting meal plan save process...');
      console.log('🔍 Current mealPlan state:', mealPlan);

      // Auto-detect dates from meals in the calendar
      const plannedDates = Object.keys(mealPlan).filter(date => {
        const dayMeals = mealPlan[date];
        if (!dayMeals || typeof dayMeals !== 'object') return false;

        const hasMeals = Object.values(dayMeals).some(meals =>
          Array.isArray(meals) && meals.length > 0
        );

        console.log(`🔍 Date ${date} has meals:`, hasMeals);
        return hasMeals;
      });

      console.log('🔍 Auto-detected planned dates:', plannedDates);

      if (plannedDates.length === 0) {
        alert('No meals found in your calendar to save. Please add some meals first by clicking on calendar dates and selecting meals.');
        return;
      }

      // Prepare selected meals for validation
      const allSelectedMeals = [];
      Object.keys(mealPlan).forEach(date => {
        if (mealPlan[date] && typeof mealPlan[date] === 'object') {
          Object.keys(mealPlan[date]).forEach(mealType => {
            if (Array.isArray(mealPlan[date][mealType]) && mealPlan[date][mealType].length > 0) {
              allSelectedMeals.push(...mealPlan[date][mealType]);
            }
          });
        }
      });

      console.log('🔍 Total selected meals:', allSelectedMeals.length);
      setPendingSavePlanName(planName);

      // Proceed directly to save
      await proceedWithSave(planName);

    } catch (error) {
      console.error('❌ Error in meal plan save process:', error);
      setValidationLoading(false);

      let errorMessage = 'Failed to process meal plan. Please try again.';

      if (error.response?.status === 401) {
        errorMessage = 'Please log in again to save meal plans.';
      } else if (error.response?.status === 400) {
        errorMessage = `Invalid data: ${error.response.data?.message || 'Please check your meal plan data.'}`;
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      alert(`❌ ${errorMessage}`);
    }
  };

  // New save meal plan function using modal data
  const saveMealPlanFromModal = async () => {
    try {
      setSavePlanLoading(true);

      // Validation
      if (!savePlanName.trim()) {
        alert('Please enter a name for your meal plan.');
        return;
      }

      if (!savePlanStartDate || !savePlanEndDate) {
        alert('Please select both start and end dates.');
        return;
      }

      if (new Date(savePlanStartDate) > new Date(savePlanEndDate)) {
        alert('Start date must be before or equal to end date.');
        return;
      }

      const token = localStorage.getItem('token');
      if (!token) {
        alert('Please log in to save meal plans.');
        return;
      }

      console.log('🔍 Starting meal plan save from modal...');

      // Get all dates in the selected range
      const dateRange = getDatesBetween(savePlanStartDate, savePlanEndDate);
      console.log('🔍 Processing date range:', dateRange);

      // Calculate total budget used and collect meals
      let totalBudgetUsed = 0;
      const mealsToSave = [];
      const processedDates = [];

      dateRange.forEach(date => {
        const dayMeals = mealPlan[date];
        if (dayMeals && typeof dayMeals === 'object') {
          let dayHasMeals = false;

          // Process each meal type for this date
          ['breakfast', 'lunch', 'dinner', 'snack'].forEach(mealType => {
            if (Array.isArray(dayMeals[mealType]) && dayMeals[mealType].length > 0) {
              dayHasMeals = true;
              dayMeals[mealType].forEach(meal => {
                if (meal && meal.name) {
                  totalBudgetUsed += meal.price || 0;

                  mealsToSave.push({
                    date: date,
                    mealType: mealType,
                    mealData: {
                      _id: meal._id || meal.id,
                      name: meal.name,
                      calories: meal.calories || 0,
                      category: meal.category || ['General'],
                      image: meal.image || '',
                      description: meal.description || '',
                      rating: meal.rating || 0,
                      protein: meal.protein || 0,
                      carbs: meal.carbs || 0,
                      fat: meal.fat || 0,
                      price: meal.price || 0,
                      dietaryTags: meal.dietaryTags || [],
                      ingredients: meal.ingredients || [],
                      instructions: meal.instructions || [],
                      instanceId: meal.instanceId || `${meal.name}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
                    }
                  });
                }
              });
            }
          });

          if (dayHasMeals) {
            processedDates.push(date);
          }
        }
      });

      if (mealsToSave.length === 0) {
        alert('No meals found in the selected date range. Please select a range that includes days with meals.');
        return;
      }

      // Prepare the meal plan data
      const mealPlanData = {
        name: savePlanName.trim(),
        startDate: savePlanStartDate,
        endDate: savePlanEndDate,
        budgetPerDay: userBudgetPerDay,
        totalBudgetUsed: totalBudgetUsed,
        riceBowlsPerDay: riceBowlsPerDay,
        meals: mealsToSave,
        mealTimes: mealTimes
      };

      console.log('📤 Final meal plan payload:', {
        name: mealPlanData.name,
        startDate: mealPlanData.startDate,
        endDate: mealPlanData.endDate,
        mealsCount: mealPlanData.meals.length,
        daysWithMeals: processedDates.length,
        totalDaysInRange: dateRange.length,
        processedDates: processedDates
      });

      // Save to backend
      const response = await apiService.saveMealPlan(mealPlanData);

      if (response.success || response.data) {
        console.log('✅ Meal plan saved successfully');

        // Track analytics
        analyticsService.trackMealPlanAction('save', {
          planName: savePlanName,
          totalMeals: mealsToSave.length,
          totalDays: processedDates.length,
          dateRange: `${savePlanStartDate} to ${savePlanEndDate}`
        });

        // Add to favorites
        try {
          const planId = response.data?.mealPlan?._id ||
                        response.data?.data?._id ||
                        response.data?._id;

          if (planId) {
            await addFavoriteMealPlan({
              _id: planId,
              name: savePlanName,
              startDate: savePlanStartDate,
              endDate: savePlanEndDate,
              totalMeals: mealsToSave.length,
              createdAt: new Date().toISOString()
            });
            console.log('✅ Added to favorites successfully');
          }
        } catch (favoriteError) {
          console.warn('⚠️ Failed to add to favorites:', favoriteError);
        }

        // Reload saved meal plans
        await loadSavedMealPlans();

        // Show success message but keep modal open
        alert(`✅ Success! Meal plan "${savePlanName}" saved successfully with ${mealsToSave.length} meals across ${processedDates.length} days!\n\nYou can now:\n• Save another meal plan with different settings\n• Add this plan to favorites\n• Close this dialog`);

      } else {
        throw new Error(response.message || 'Failed to save meal plan');
      }

    } catch (error) {
      console.error('❌ Error saving meal plan:', error);

      let errorMessage = 'Failed to save meal plan. Please try again.';
      if (error.response?.status === 401) {
        errorMessage = 'Please log in again to save meal plans.';
      } else if (error.response?.status === 400) {
        errorMessage = `Invalid data: ${error.response.data?.message || 'Please check your meal plan data.'}`;
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      alert(`❌ ${errorMessage}`);
    } finally {
      setSavePlanLoading(false);
    }
  };

  // Save meal plan directly to favorites (simplified approach)
  const saveToFavoritesFromModal = async () => {
    try {
      setSaveToFavoritesLoading(true);

      // Validation
      if (!savePlanName.trim()) {
        alert('Please enter a name for your meal plan.');
        return;
      }

      if (!savePlanStartDate || !savePlanEndDate) {
        alert('Please select both start and end dates.');
        return;
      }

      if (new Date(savePlanStartDate) > new Date(savePlanEndDate)) {
        alert('Start date must be before or equal to end date.');
        return;
      }

      console.log('🔍 Saving meal plan directly to favorites...');

      // Get all dates in the selected range
      const dateRange = getDatesBetween(savePlanStartDate, savePlanEndDate);
      console.log('🔍 Processing date range:', dateRange);

      // Collect meals and calculate stats
      const mealsToSave = [];
      const processedDates = [];
      let totalBudgetUsed = 0;

      dateRange.forEach(date => {
        const dayMeals = mealPlan[date];
        if (dayMeals && typeof dayMeals === 'object') {
          let dayHasMeals = false;

          // Process each meal type for this date
          ['breakfast', 'lunch', 'dinner', 'snack'].forEach(mealType => {
            if (Array.isArray(dayMeals[mealType]) && dayMeals[mealType].length > 0) {
              dayHasMeals = true;
              dayMeals[mealType].forEach(meal => {
                if (meal && meal.name) {
                  totalBudgetUsed += meal.price || 0;
                  mealsToSave.push({
                    date: date,
                    mealType: mealType,
                    meal: meal
                  });
                }
              });
            }
          });

          if (dayHasMeals) {
            processedDates.push(date);
          }
        }
      });

      if (mealsToSave.length === 0) {
        alert('No meals found in the selected date range. Please select a range that includes days with meals.');
        return;
      }

      // Calculate total calories and meal types for favorites
      const totalCalories = mealsToSave.reduce((total, meal) => total + (meal.meal?.calories || 0), 0);
      const mealTypes = [...new Set(mealsToSave.map(meal => meal.mealType))];

      // Create a comprehensive meal plan object for favorites
      const favoriteMealPlan = {
        name: savePlanName.trim(),
        startDate: savePlanStartDate,
        endDate: savePlanEndDate,
        totalMeals: mealsToSave.length,
        totalDays: processedDates.length,
        totalCalories: totalCalories,
        totalBudget: totalBudgetUsed,
        meals: mealsToSave,
        mealTimes: mealTimes,
        mealTypes: mealTypes,
        riceBowlsPerDay: riceBowlsPerDay,
        createdAt: new Date().toISOString(),
        isFavorite: true,
        source: 'calendar'
      };

      console.log('📤 Adding comprehensive meal plan to favorites:', {
        name: favoriteMealPlan.name,
        dateRange: `${favoriteMealPlan.startDate} to ${favoriteMealPlan.endDate}`,
        totalMeals: favoriteMealPlan.totalMeals,
        totalDays: favoriteMealPlan.totalDays,
        totalCalories: favoriteMealPlan.totalCalories
      });

      console.log('📤 Full meal plan data being sent:', JSON.stringify(favoriteMealPlan, null, 2));

      // Add to favorites using context
      const result = await addFavoriteMealPlan(favoriteMealPlan);

      if (result && result.success === false) {
        throw new Error(result.error || 'Failed to add to favorites');
      }

      console.log('✅ Successfully added to favorites');

      // Track analytics
      analyticsService.trackMealPlanAction('add_to_favorites', {
        planName: savePlanName,
        totalMeals: mealsToSave.length,
        totalDays: processedDates.length,
        dateRange: `${savePlanStartDate} to ${savePlanEndDate}`,
        source: 'modal_direct_to_favorites'
      });

      // Show success message but keep modal open
      alert(`⭐ Success! Meal plan "${savePlanName}" added to favorites with ${mealsToSave.length} meals across ${processedDates.length} days!\n\nYou can now:\n• Add to favorites again with a different name\n• Save the meal plan to your account\n• Close this dialog`);

    } catch (error) {
      console.error('❌ Error adding meal plan to favorites:', error);

      let errorMessage = 'Failed to add meal plan to favorites. Please try again.';

      // Check for specific error types
      if (error.message) {
        errorMessage = error.message;
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.status === 400) {
        errorMessage = 'Invalid meal plan data. Please check your selections and try again.';
      } else if (error.response?.status === 401) {
        errorMessage = 'Please log in again to add favorites.';
      }

      console.error('❌ Detailed error info:', {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
        favoriteMealPlan
      });

      alert(`❌ ${errorMessage}`);
    } finally {
      setSaveToFavoritesLoading(false);
    }
  };

  const proceedWithSave = async (planName) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        alert('Please log in to save meal plans');
        return;
      }

      // Auto-detect dates from meals in the calendar
      console.log('🔍 Current mealPlan state:', mealPlan);

      // Get all dates that have actual meals planned
      const plannedDates = Object.keys(mealPlan).filter(date => {
        const dayMeals = mealPlan[date];
        if (!dayMeals || typeof dayMeals !== 'object') return false;

        // Check if this date has any meals in any meal type
        const hasMeals = Object.values(dayMeals).some(meals =>
          Array.isArray(meals) && meals.length > 0
        );

        console.log(`🔍 Date ${date} has meals:`, hasMeals, dayMeals);
        return hasMeals;
      });

      console.log('🔍 All planned dates found:', plannedDates);

      if (plannedDates.length === 0) {
        alert('No meals found in your calendar to save. Please add some meals first by clicking on calendar dates and selecting meals.');
        return;
      }

      // Sort dates chronologically and auto-configure start/end dates
      plannedDates.sort((a, b) => new Date(a) - new Date(b));
      const startDate = plannedDates[0];
      const endDate = plannedDates[plannedDates.length - 1];

      console.log('🔍 Auto-configured date range:', {
        startDate,
        endDate,
        totalDays: plannedDates.length,
        dateSpan: `${startDate} to ${endDate}`
      });

      // Validate that we have proper date strings
      if (!startDate || !endDate) {
        console.error('❌ Failed to auto-configure dates:', { startDate, endDate, plannedDates });
        alert('Unable to determine date range from your meal plan. Please try adding meals again.');
        return;
      }

      // Validate date format (should be YYYY-MM-DD)
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (!dateRegex.test(startDate) || !dateRegex.test(endDate)) {
        console.error('❌ Invalid date format detected:', { startDate, endDate });
        alert('Invalid date format detected. Please refresh the page and try again.');
        return;
      }

      // Calculate total budget used
      let totalBudgetUsed = 0;
      Object.keys(mealPlan).forEach(date => {
        Object.keys(mealPlan[date]).forEach(mealType => {
          if (mealPlan[date][mealType] && mealPlan[date][mealType].length > 0) {
            mealPlan[date][mealType].forEach(meal => {
              totalBudgetUsed += meal.price || 0;
            });
          }
        });
      });

      // Validate that we have valid dates
      if (!startDate || !endDate) {
        alert('Invalid date range. Please ensure you have meals planned for valid dates.');
        return;
      }

      // Prepare the meal plan data
      const mealPlanData = {
        name: planName,
        startDate: startDate,
        endDate: endDate,
        budgetPerDay: userBudgetPerDay,
        totalBudgetUsed: totalBudgetUsed,
        riceBowlsPerDay: riceBowlsPerDay, // Include rice bowls data
        meals: [],
        mealTimes: mealTimes
      };

      // Convert mealPlan object to the format expected by backend
      // Only process dates that have actual meals (the plannedDates we found)
      plannedDates.forEach(date => {
        const dayMeals = mealPlan[date];
        if (!dayMeals || typeof dayMeals !== 'object') {
          console.warn(`⚠️ Skipping date ${date} - no meal data found`);
          return;
        }

        // Process each meal type for this date
        ['breakfast', 'lunch', 'dinner', 'snack'].forEach(mealType => {
          if (Array.isArray(dayMeals[mealType]) && dayMeals[mealType].length > 0) {
            dayMeals[mealType].forEach(meal => {
              // Validate meal data
              if (!meal || !meal.name) {
                console.warn(`⚠️ Skipping invalid meal on ${date} ${mealType}:`, meal);
                return;
              }

              console.log(`✅ Adding meal: ${meal.name} for ${date} ${mealType}`);

              mealPlanData.meals.push({
                date: date,
                mealType: mealType,
                mealData: {
                  _id: meal._id || meal.id,
                  name: meal.name,
                  calories: meal.calories || 0,
                  category: meal.category || ['General'],
                  image: meal.image || '',
                  description: meal.description || '',
                  rating: meal.rating || 0,
                  protein: meal.protein || 0,
                  carbs: meal.carbs || 0,
                  fat: meal.fat || 0,
                  price: meal.price || 0,
                  dietaryTags: meal.dietaryTags || [],
                  ingredients: meal.ingredients || [],
                  instructions: meal.instructions || [],
                  instanceId: meal.instanceId || `${meal.name}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
                }
              });
            });
          }
        });
      });

      // Final validation before sending
      if (mealPlanData.meals.length === 0) {
        alert('No valid meals found to save. Please add some meals to your calendar first by clicking on dates and selecting meals.');
        return;
      }

      // Comprehensive logging for debugging
      console.log('📤 Final meal plan payload ready:', {
        name: mealPlanData.name,
        startDate: mealPlanData.startDate,
        endDate: mealPlanData.endDate,
        mealsCount: mealPlanData.meals.length,
        dateRange: `${mealPlanData.startDate} to ${mealPlanData.endDate}`,
        daysSpanned: plannedDates.length,
        mealsByDate: plannedDates.map(date => ({
          date,
          mealCount: mealPlanData.meals.filter(m => m.date === date).length
        })),
        hasValidDates: !!(mealPlanData.startDate && mealPlanData.endDate),
        hasValidName: !!(mealPlanData.name && mealPlanData.name.trim()),
        payloadSize: JSON.stringify(mealPlanData).length
      });

      // Final payload validation
      if (!mealPlanData.name || !mealPlanData.name.trim()) {
        alert('Please provide a valid name for your meal plan.');
        return;
      }

      if (!mealPlanData.startDate || !mealPlanData.endDate) {
        alert('Invalid date range. Please try again.');
        return;
      }

      const response = await apiService.saveMealPlan(mealPlanData);

      console.log('📥 Save response received:', response);

      if (response.success || response.data) {
        // Track meal plan save analytics
        analyticsService.trackMealPlanAction('save', {
          planName,
          totalMeals: Object.values(mealPlan).reduce((total, dayPlan) => {
            return total + Object.values(dayPlan).reduce((dayTotal, meals) => dayTotal + meals.length, 0);
          }, 0),
          totalDays: Object.keys(mealPlan).length,
          totalCalories: Object.keys(mealPlan).reduce((total, date) => total + calculateDailyCalories(date), 0)
        });

        // Close validation modal if open
        setShowValidationModal(false);
        setValidationResult(null);
        setPendingSavePlanName('');

        alert('Meal plan saved successfully!');
        // Reload saved meal plans
        await loadSavedMealPlans();
      }
    } catch (error) {
      console.error('❌ Error saving meal plan:', error);

      let errorMessage = 'Failed to save meal plan. Please try again.';

      if (error.response?.status === 401) {
        errorMessage = 'Authentication failed. Please log in again.';
      } else if (error.response?.status === 400) {
        errorMessage = `Invalid meal plan data: ${error.response.data?.message || 'Please check your meal selections.'}`;
      } else if (error.response?.status === 404) {
        errorMessage = 'Meal plan not found. Please try creating a new one.';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      alert(`❌ ${errorMessage}`);
    }
  };

  // Add this function to load saved meal plans
  const loadSavedMealPlans = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const response = await axios.get(
        `${API_BASE_URL}/meal-plans`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (response.data && Array.isArray(response.data)) {
        console.log('Loaded saved meal plans:', response.data);
        setSavedMealPlans(response.data);

        // Convert database meal plans to calendar format and merge with existing meal plan
        const databaseMealPlans = convertDatabaseMealPlansToCalendarFormat(response.data);
        console.log('Converted database meal plans:', databaseMealPlans);

        setMealPlan(prevPlan => {
          // Merge database meal plans with existing local meal plans
          const mergedPlan = { ...prevPlan };
          console.log('Previous meal plan:', prevPlan);

          Object.keys(databaseMealPlans).forEach(date => {
            if (!mergedPlan[date]) {
              mergedPlan[date] = databaseMealPlans[date];
            } else {
              // Merge meals for the same date
              ['breakfast', 'lunch', 'dinner', 'snack'].forEach(mealType => {
                if (databaseMealPlans[date][mealType] && databaseMealPlans[date][mealType].length > 0) {
                  if (!mergedPlan[date][mealType]) {
                    mergedPlan[date][mealType] = [];
                  }
                  // Add database meals that aren't already in the local plan
                  databaseMealPlans[date][mealType].forEach(dbMeal => {
                    const existsInLocal = mergedPlan[date][mealType].some(localMeal =>
                      localMeal.instanceId === dbMeal.instanceId ||
                      (localMeal.name === dbMeal.name && localMeal._id === dbMeal._id)
                    );
                    if (!existsInLocal) {
                      mergedPlan[date][mealType].push(dbMeal);
                    }
                  });
                }
              });
            }
          });

          return mergedPlan;
        });
      }
    } catch (error) {
      console.error('Error loading saved meal plans:', error);
    }
  };

  // Convert database meal plans to calendar format
  const convertDatabaseMealPlansToCalendarFormat = (databasePlans) => {
    const calendarFormat = {};
    console.log('Converting database plans:', databasePlans);

    databasePlans.forEach(plan => {
      const date = plan.date;
      console.log('Processing plan for date:', date, plan);

      if (!calendarFormat[date]) {
        calendarFormat[date] = {
          breakfast: [],
          lunch: [],
          dinner: [],
          snack: []
        };
      }

      // Handle the new format with breakfast, lunch, dinner arrays
      ['breakfast', 'lunch', 'dinner', 'snack'].forEach(mealType => {
        if (plan[mealType] && Array.isArray(plan[mealType])) {
          plan[mealType].forEach(meal => {
            calendarFormat[date][mealType].push({
              ...meal,
              instanceId: meal.instanceId || `${meal.name}-${Date.now()}`
            });
          });
        }
      });

      // Handle the old format with meals array (for backward compatibility)
      if (plan.meals && Array.isArray(plan.meals)) {
        plan.meals.forEach(mealItem => {
          if (mealItem.meal && mealItem.mealType) {
            const mealType = mealItem.mealType;
            if (['breakfast', 'lunch', 'dinner', 'snack'].includes(mealType)) {
              calendarFormat[date][mealType].push({
                ...mealItem.meal,
                instanceId: mealItem.meal.instanceId || `${mealItem.meal.name}-${Date.now()}`
              });
            }
          }
        });
      }
    });

    return calendarFormat;
  };

  // Add current meal plan to favorites (following mobile app flow)
  const addCurrentMealPlanToFavorites = async () => {
    if (isAddingToFavorites) return; // Prevent double-clicking

    try {
      setIsAddingToFavorites(true);
      // Get all dates that have meals planned
      const plannedDates = Object.keys(mealPlan).filter(date => {
        return Object.values(mealPlan[date]).some(meals => meals && meals.length > 0);
      });

      if (plannedDates.length === 0) {
        alert('No meals planned to add to favorites');
        return;
      }

      // Sort dates to get start and end (ensure they're in YYYY-MM-DD format)
      plannedDates.sort();
      let startDate = plannedDates[0];
      let endDate = plannedDates[plannedDates.length - 1];

      // Ensure dates are in YYYY-MM-DD format
      if (startDate && !startDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
        startDate = new Date(startDate).toISOString().split('T')[0];
      }
      if (endDate && !endDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
        endDate = new Date(endDate).toISOString().split('T')[0];
      }

      console.log('📅 Planned dates:', plannedDates);
      console.log('📅 Start date:', startDate, 'End date:', endDate);

      // Validate dates
      if (!startDate || !endDate || startDate === 'Invalid Date' || endDate === 'Invalid Date') {
        alert('Invalid date range. Please ensure you have meals planned for valid dates.');
        return;
      }

      // Generate a unique name for the meal plan
      const now = new Date();
      const planName = `Meal Plan ${now.toLocaleDateString()} ${now.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })}`;

      // Step 1: First save the meal plan to get an ObjectId (following mobile app flow)
      const mealPlanData = {
        name: planName,
        startDate: startDate,
        endDate: endDate,
        dietaryPreference: selectedPreference || 'all',
        meals: [],
        mealTimes: mealTimes || {
          breakfast: '08:00',
          lunch: '12:00',
          dinner: '18:00',
          snack: '15:00'
        }
      };

      // Convert mealPlan object to the format expected by backend
      Object.keys(mealPlan).forEach(date => {
        // Ensure date is in YYYY-MM-DD format
        let formattedDate = date;
        if (!date.match(/^\d{4}-\d{2}-\d{2}$/)) {
          formattedDate = new Date(date).toISOString().split('T')[0];
        }

        Object.keys(mealPlan[date]).forEach(mealType => {
          if (mealPlan[date][mealType] && mealPlan[date][mealType].length > 0) {
            mealPlan[date][mealType].forEach(meal => {
              mealPlanData.meals.push({
                date: formattedDate,
                mealType: mealType,
                mealData: {
                  _id: meal._id || meal.id,
                  name: meal.name,
                  calories: meal.calories || 0,
                  category: meal.category || ['General'],
                  image: meal.image || '',
                  description: meal.description || '',
                  rating: meal.rating || 0,
                  protein: meal.protein || 0,
                  carbs: meal.carbs || 0,
                  fat: meal.fat || 0,
                  dietaryTags: meal.dietaryTags || [],
                  ingredients: meal.ingredients || [],
                  instructions: meal.instructions || [],
                  instanceId: meal.instanceId || `${meal.name}-${Date.now()}`
                }
              });
            });
          }
        });
      });

      // Validate that we have meals to save
      if (mealPlanData.meals.length === 0) {
        alert('No meals found to add to favorites. Please add some meals first.');
        return;
      }

      console.log('💾 Saving meal plan first to get ObjectId:', {
        name: mealPlanData.name,
        startDate: mealPlanData.startDate,
        endDate: mealPlanData.endDate,
        mealsCount: mealPlanData.meals.length,
        firstMeal: mealPlanData.meals[0],
        fullMealPlanData: JSON.stringify(mealPlanData, null, 2)
      });

      // Save the meal plan first to get the ObjectId
      const token = localStorage.getItem('token');
      if (!token) {
        alert('Please log in to save meal plans');
        return;
      }

      // Final validation before sending
      if (!mealPlanData.name || !mealPlanData.startDate || !mealPlanData.endDate || !mealPlanData.meals || mealPlanData.meals.length === 0) {
        console.error('❌ Final validation failed:', {
          hasName: !!mealPlanData.name,
          hasStartDate: !!mealPlanData.startDate,
          hasEndDate: !!mealPlanData.endDate,
          hasMeals: !!mealPlanData.meals,
          mealsCount: mealPlanData.meals?.length || 0
        });
        alert('❌ Invalid meal plan data. Please ensure you have:\n• A valid name\n• Valid start and end dates\n• At least one meal planned');
        return;
      }

      console.log('Making save request with token:', token ? 'Present' : 'Missing');
      console.log('Request payload:', JSON.stringify(mealPlanData, null, 2));

      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const saveResponse = await axios.post(
        `${API_BASE_URL}/meal-plans/save`,
        mealPlanData,
        { headers: { Authorization: `Bearer ${token}` } }
      );

      console.log('Meal plan save response:', saveResponse.data);

      // Extract the plan ID from the response (following mobile app logic)
      const planId = saveResponse.data?.savedPlans?.[0]?._id ||
                    saveResponse.data?.mealPlan?._id ||
                    saveResponse.data?._id;

      if (!planId) {
        console.error('No plan ID found in response:', saveResponse.data);
        throw new Error('Failed to save meal plan - no ID returned');
      }

      const mealPlanId = planId;
      console.log('Using meal plan ID for favorites:', mealPlanId);

      // Step 2: Calculate plan details for favorites (following mobile app structure)
      const totalMeals = mealPlanData.meals.length;
      const totalCalories = mealPlanData.meals.reduce((sum, mealEntry) => {
        return sum + (mealEntry.mealData?.calories || 0);
      }, 0);
      const mealTypes = [...new Set(mealPlanData.meals.map(mealEntry => mealEntry.mealType))];

      // Step 3: Add to favorites with the proper ObjectId
      const favoriteData = {
        mealPlanId: mealPlanId,
        name: planName,
        date: startDate,
        totalCalories,
        totalMeals,
        mealTypes
      };

      console.log('Adding to favorites with data:', favoriteData);

      const result = await addFavoriteMealPlan(favoriteData);

      if (result.success) {
        alert(`✅ Success!\n\nMeal plan "${planName}" has been saved and added to your favorites!\n\n📊 Plan Details:\n• ${totalMeals} meals planned\n• ${totalCalories} total calories\n• Meal types: ${mealTypes.join(', ')}`);
        // Reload saved meal plans to show the new one
        await loadSavedMealPlans();
      } else {
        alert(`❌ Error adding to favorites:\n${result.error || 'Unknown error occurred'}`);
      }
    } catch (error) {
      console.error('Error adding meal plan to favorites:', error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);

      // Provide more specific error messages
      let errorMessage = 'Failed to add meal plan to favorites. Please try again.';

      if (error.response?.status === 401) {
        errorMessage = 'Please log in again to save meal plans.';
      } else if (error.response?.status === 400) {
        const backendMessage = error.response.data?.message;
        const receivedData = error.response.data?.received;
        errorMessage = backendMessage || 'Invalid meal plan data.';
        if (receivedData) {
          console.error('Backend received data:', receivedData);
          errorMessage += `\n\nDebug info: ${JSON.stringify(receivedData)}`;
        }
      } else if (error.response?.status === 500) {
        errorMessage = `Server error: ${error.response.data?.message || 'Internal server error'}`;
      } else if (error.message.includes('ObjectId') || error.message.includes('no ID returned')) {
        errorMessage = 'Failed to save meal plan. Please try again.';
      } else if (!error.response) {
        errorMessage = 'Network error. Please check your connection and try again.';
      }

      alert(`❌ Error: ${errorMessage}`);
    } finally {
      setIsAddingToFavorites(false);
    }
  };
  // Fetch all meals from API (for meal selector)
  useEffect(() => {
    const fetchMeals = async () => {
      try {
        setLoading(true);
        const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
        const data = await axios.get(`${API_BASE_URL}/meals`);
        setMeals(data.data || []);
        setError(null);
      } catch (err) {
        setError("Failed to load meals. Please try again later.");
      } finally {
        setLoading(false);
      }
    };
    fetchMeals();
    // loadSavedMealPlans is now called in the initialization useEffect
  }, []);

  // Load meal plans from backend API
  const loadMealPlansFromAPI = async () => {
    try {
      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const response = await axios.get(`${API_BASE_URL}/meal-plans`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const backendMealPlans = response.data || [];
      console.log('Loaded meal plans from backend:', backendMealPlans);

      // Convert backend meal plans to website format
      const convertedMealPlan = {};
      const convertedRiceBowls = {};

      backendMealPlans.forEach(plan => {
        const dateStr = plan.date.split('T')[0]; // Convert to YYYY-MM-DD format

        // Initialize day structure
        if (!convertedMealPlan[dateStr]) {
          convertedMealPlan[dateStr] = {
            breakfast: [],
            lunch: [],
            dinner: [],
            snack: []
          };
        }

        // Add rice bowls data
        if (plan.riceBowls > 0) {
          convertedRiceBowls[dateStr] = plan.riceBowls;
        }

        // Convert meals from backend format
        if (plan.meals && Array.isArray(plan.meals)) {
          plan.meals.forEach(mealItem => {
            const mealType = mealItem.mealType;
            if (convertedMealPlan[dateStr][mealType]) {
              convertedMealPlan[dateStr][mealType].push({
                ...mealItem.meal,
                instanceId: `${mealItem.meal._id}-${Date.now()}-${Math.random()}`
              });
            }
          });
        }

        // Also handle the old format (breakfast, lunch, dinner, snack arrays)
        ['breakfast', 'lunch', 'dinner', 'snack'].forEach(mealType => {
          if (plan[mealType] && Array.isArray(plan[mealType])) {
            plan[mealType].forEach(meal => {
              convertedMealPlan[dateStr][mealType].push({
                ...meal,
                instanceId: meal.instanceId || `${meal._id || meal.id}-${Date.now()}-${Math.random()}`
              });
            });
          }
        });
      });

      // Merge with existing localStorage data
      const savedMealPlan = localStorage.getItem('mealPlan');
      const existingMealPlan = savedMealPlan ? JSON.parse(savedMealPlan) : {};

      const mergedMealPlan = { ...existingMealPlan, ...convertedMealPlan };
      setMealPlan(mergedMealPlan);

      // Set rice bowls data
      setRiceBowlsPerDay(convertedRiceBowls);

      console.log('Merged meal plan:', mergedMealPlan);
      console.log('Rice bowls data:', convertedRiceBowls);

      // Debug: Check if rice bowls are being set correctly
      Object.keys(convertedRiceBowls).forEach(date => {
        console.log(`Website: Date ${date} has ${convertedRiceBowls[date]} rice bowls`);
      });

    } catch (error) {
      console.error('Error loading meal plans from API:', error);
      // Continue with localStorage data if API fails
    }
  };

  // Save rice bowls to backend (simplified approach)
  const saveRiceBowlsToBackend = async (date, riceBowls) => {
    try {
      console.log(`Saving rice bowls for ${date}: ${riceBowls}`);
      // For now, just log the rice bowls change
      // The rice bowls will be saved when the meal plan is saved via saveMealPlan function
      // or when individual meals are added via addMealToPlan function
    } catch (error) {
      console.error('Error saving rice bowls to backend:', error);
    }
  };

  // Load user dietary preferences on mount
  useEffect(() => {
    loadUserPreferences();
    loadFamilyMembers();
  }, []);

  // Cleanup tooltip timeout on unmount
  useEffect(() => {
    return () => {
      if (tooltipTimeout) {
        clearTimeout(tooltipTimeout);
      }
    };
  }, [tooltipTimeout]);

  // Trigger AI analysis when meal selector opens and meals are available
  useEffect(() => {
    if (showMealSelector && meals.length > 0 && familyMembers.length >= 0) {
      const filteredMeals = getFilteredMeals();
      if (filteredMeals.length > 0) {
        fetchMealAnalysis(filteredMeals);
      }
    }
  }, [showMealSelector, meals, familyMembers, searchTerm, dietaryPreference]);

  // Notification/alert effects (unchanged)
  useEffect(() => {
    const checkMealTimeAlerts = () => {
      const now = new Date();
      const currentHour = now.getHours();
      const currentMinute = now.getMinutes();
      const today = formatDate(now);

      if (mealPlan[today]) {
        mealTypes.forEach(mealType => {
          if (mealPlan[today][mealType] && mealPlan[today][mealType].length > 0) {
            const mealTime = mealTimes[mealType] || defaultMealTimes[mealType];
            const [mealHour, mealMinute] = mealTime.split(':').map(Number);

            const isExactlyMealTime = (
              currentHour === mealHour &&
              currentMinute >= mealMinute &&
              currentMinute <= mealMinute + 1
            );

            const allMealsCompleted = mealPlan[today][mealType].every(meal =>
              isMealCompleted(today, mealType, meal.instanceId)
            );

            if (isExactlyMealTime && !allMealsCompleted) {
              const alertExists = missedMealAlerts.some(
                alert => alert.date === today && alert.mealType === mealType
              );

              if (!alertExists) {
                if ('Notification' in window && Notification.permission === 'granted') {
                  new Notification(`Time for ${mealType}!`, {
                    body: `It's time for your scheduled ${mealType} (${mealTime})`,
                    icon: '/favicon.ico'
                  });
                }

                setMissedMealAlerts(prev => [
                  ...prev,
                  { date: today, mealType, time: mealTime }
                ]);
                setShowMissedMealAlert(true);
              }
            }
          }
        });
      }
    };

    if ('Notification' in window && Notification.permission !== 'denied') {
      Notification.requestPermission();
    }

    const intervalId = setInterval(checkMealTimeAlerts, 30000);
    checkMealTimeAlerts();
    return () => clearInterval(intervalId);
  }, [mealPlan, mealTimes, missedMealAlerts]);

  useEffect(() => {
    const savedCompletedMeals = localStorage.getItem('completedMeals');
    if (savedCompletedMeals) {
      setCompletedMeals(JSON.parse(savedCompletedMeals));
    }
  }, []);

  useEffect(() => {
    if (Object.keys(completedMeals).length > 0) {
      localStorage.setItem('completedMeals', JSON.stringify(completedMeals));
    }
  }, [completedMeals]);

  useEffect(() => {
    const initializeMealPlan = async () => {
      // Load from localStorage first
      const savedMealPlan = localStorage.getItem('mealPlan');
      const savedLockedDates = localStorage.getItem('lockedDates');
      const savedMealTimes = localStorage.getItem('mealTimes');

      if (savedMealPlan) {
        setMealPlan(JSON.parse(savedMealPlan));
      }
      if (savedLockedDates) {
        setLockedDates(JSON.parse(savedLockedDates));
      }
      if (savedMealTimes) {
        setMealTimes(JSON.parse(savedMealTimes));
      } else {
        setMealTimes(defaultMealTimes);
      }

      const today = new Date();
      today.setHours(0, 0, 0, 0);
      setSelectedDate(formatDate(today));
      checkForMissedMealPlans();

      // Then load and merge database meal plans
      await loadSavedMealPlans();

      // Load actual meal plans from backend API
      await loadMealPlansFromAPI();
    };

    initializeMealPlan();
  }, []);

  useEffect(() => {
    if (Object.keys(mealPlan).length > 0) {
      localStorage.setItem('mealPlan', JSON.stringify(mealPlan));
    }
  }, [mealPlan]);

  useEffect(() => {
    if (Object.keys(lockedDates).length > 0) {
      localStorage.setItem('lockedDates', JSON.stringify(lockedDates));
    }
  }, [lockedDates]);

  useEffect(() => {
    if (Object.keys(mealTimes).length > 0) {
      localStorage.setItem('mealTimes', JSON.stringify(mealTimes));
    }
  }, [mealTimes]);

  // Update save plan preview when dates change
  useEffect(() => {
    updateSavePlanPreview();
  }, [savePlanStartDate, savePlanEndDate, mealPlan]);

  useEffect(() => {
    if (showSaveSuccess) {
      const timer = setTimeout(() => {
        setShowSaveSuccess(false);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [showSaveSuccess]);

  useEffect(() => {
    if (missedMealNotification) {
      const timer = setTimeout(() => {
        setMissedMealNotification(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [missedMealNotification]);

  useEffect(() => {
    const checkMealTimeAlerts = () => {
      const now = new Date();
      const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
      const today = formatDate(now);

      if (mealPlan[today]) {
        mealTypes.forEach(mealType => {
          if (mealPlan[today][mealType] && mealPlan[today][mealType].length > 0) {
            const mealTime = mealTimes[mealType];
            if (currentTime > mealTime && minutesDifference(currentTime, mealTime) <= 30) {
              const alertExists = missedMealAlerts.some(
                alert => alert.date === today && alert.mealType === mealType
              );

              if (!alertExists) {
                setMissedMealAlerts(prev => [
                  ...prev,
                  { date: today, mealType, time: mealTime }
                ]);
                setShowMissedMealAlert(true);
              }
            }
          }
        });
      }
    };

    const intervalId = setInterval(checkMealTimeAlerts, 60000);
    checkMealTimeAlerts();
    return () => clearInterval(intervalId);
  }, [mealPlan, mealTimes, missedMealAlerts]);

  useEffect(() => {
    const savedPrefs = localStorage.getItem('mealPlanUserPrefs');
    if (savedPrefs) {
      const { budgetPerDay, mealDaysPerWeek, defaultRiceBowls } = JSON.parse(savedPrefs);
      setUserBudgetPerDay(budgetPerDay || 500);
      setUserMealDaysPerWeek(mealDaysPerWeek || 7);
      setDefaultRiceBowls(defaultRiceBowls || 1);
      setShowPreferencesModal(false);
    } else {
      setShowPreferencesModal(true);
    }
  }, []);
  // Handle state passed from sidebar (selected meal for meal planning)
  useEffect(() => {
    if (location.state?.selectedMeal && location.state?.openMealSelector) {
      const today = new Date();
      const todayStr = formatDate(today);
      setSelectedDate(todayStr);
      setSelectedMealType("breakfast"); // Default to breakfast
      setShowMealSelector(true);

      // Add the selected meal to today's breakfast
      const selectedMeal = location.state.selectedMeal;
      addMealToPlan(selectedMeal);

      // Clear the state to prevent re-triggering
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  // Initialize scrollbar state when meal selector opens
  useEffect(() => {
    if (showMealSelector && scrollContainerRef.current) {
      const timer = setTimeout(() => {
        updateScrollbarState();
      }, 100); // Small delay to ensure DOM is rendered

      return () => clearTimeout(timer);
    }
  }, [showMealSelector]);

  // Helper to filter meals by user dietary preferences from profile
  const filterMealsByUserPreferences = (meals, userPreferences) => {
    let filtered = meals;

    // Filter by dietary restrictions (inclusive - user wants ONLY these types)
    if (userPreferences.restrictions && userPreferences.restrictions.length > 0) {
      filtered = filtered.filter(meal => {
        const dietType = meal.dietType || meal.dietaryAttributes || {};

        return userPreferences.restrictions.every(restriction => {
          switch(restriction) {
            case 'Vegetarian':
              return dietType.isVegetarian || meal.dietaryTags?.includes("vegetarian");
            case 'Vegan':
              return dietType.isVegan || meal.dietaryTags?.includes("vegan");
            case 'Gluten-Free':
              return dietType.isGlutenFree || meal.dietaryTags?.includes("gluten-free");
            case 'Dairy-Free':
              return dietType.isDairyFree || meal.dietaryTags?.includes("dairy-free");
            case 'Nut-Free':
              return dietType.isNutFree || meal.dietaryTags?.includes("nut-free");
            case 'Low-Carb':
              return dietType.isLowCarb || meal.dietaryTags?.includes("low-carb");
            case 'Keto':
              return dietType.isKeto || meal.dietaryTags?.includes("keto");
            case 'Pescatarian':
              return dietType.isPescatarian || meal.dietaryTags?.includes("pescatarian");
            case 'Halal':
              return dietType.isHalal || meal.dietaryTags?.includes("halal");
            default:
              return true;
          }
        });
      });
    }

    // Filter out allergies (exclusive - user does NOT want these)
    if (userPreferences.allergies && userPreferences.allergies.length > 0) {
      filtered = filtered.filter(meal => {
        const ingredients = meal.ingredients || [];
        const allergens = meal.allergens || [];

        return !userPreferences.allergies.some(allergy => {
          // Check if allergy is in allergens array
          if (allergens.includes(allergy)) return true;

          // Check if allergy is mentioned in ingredients
          return ingredients.some(ingredient =>
            typeof ingredient === 'string' &&
            ingredient.toLowerCase().includes(allergy.toLowerCase())
          );
        });
      });
    }

    // Filter out disliked ingredients
    if (userPreferences.dislikedIngredients && userPreferences.dislikedIngredients.length > 0) {
      filtered = filtered.filter(meal => {
        const ingredients = meal.ingredients || [];

        return !userPreferences.dislikedIngredients.some(disliked => {
          return ingredients.some(ingredient =>
            typeof ingredient === 'string' &&
            ingredient.toLowerCase().includes(disliked.toLowerCase())
          );
        });
      });
    }

    return filtered;
  };

  // Helper to filter meals by dietary preference
  const filterMealsByPreference = (meals, preference) => {
    switch (preference) {
      case "highProtein":
      case "high-protein":
        return meals.filter(meal => meal.protein && meal.protein > 15);
      case "lowSodium":
      case "low-sodium":
        return meals.filter(meal => meal.sodium && meal.sodium < 500);
      case "lowCalorie":
        return meals.filter(meal => meal.calories < 300);
      case "vegetarian":
        return meals.filter(meal =>
          meal.dietaryTags?.includes("vegetarian") ||
          (
            !meal.ingredients ||
            !meal.ingredients.some(ingredient =>
              /beef|chicken|pork|fish|meat|seafood/i.test(ingredient)
            )
          )
        );
      case "vegan":
        return meals.filter(meal =>
          meal.dietaryTags?.includes("vegan") ||
          (
            !meal.ingredients ||
            !meal.ingredients.some(ingredient =>
              /beef|chicken|pork|fish|meat|seafood|egg|milk|cheese|butter|honey/i.test(ingredient)
            )
          )
        );
      case "dairy-free":
        return meals.filter(meal =>
          meal.dietaryTags?.includes("dairy-free") ||
          (
            !meal.ingredients ||
            !meal.ingredients.some(ingredient =>
              /milk|cheese|butter|cream|yogurt|dairy/i.test(ingredient)
            )
          )
        );
      case "egg-free":
        return meals.filter(meal =>
          meal.dietaryTags?.includes("egg-free") ||
          (
            !meal.ingredients ||
            !meal.ingredients.some(ingredient =>
              /egg/i.test(ingredient)
            )
          )
        );
      case "gluten-free":
        return meals.filter(meal =>
          meal.dietaryTags?.includes("gluten-free") ||
          (
            !meal.ingredients ||
            !meal.ingredients.some(ingredient =>
              /wheat|barley|rye|malt|gluten|bread|pasta|flour/i.test(ingredient)
            )
          )
        );
      case "soy-free":
        return meals.filter(meal =>
          meal.dietaryTags?.includes("soy-free") ||
          (
            !meal.ingredients ||
            !meal.ingredients.some(ingredient =>
              /soy|tofu|edamame|soybean/i.test(ingredient)
            )
          )
        );
      case "nut-free":
        return meals.filter(meal =>
          meal.dietaryTags?.includes("nut-free") ||
          (
            !meal.ingredients ||
            !meal.ingredients.some(ingredient =>
              /almond|cashew|walnut|pecan|hazelnut|nut|peanut|pistachio/i.test(ingredient)
            )
          )
        );
      case "low-carb":
        return meals.filter(meal => meal.carbs && meal.carbs < 20);
      case "low-sugar":
        return meals.filter(meal => meal.sugar && meal.sugar < 5);
      case "sugar-free":
        return meals.filter(meal => (meal.sugar || 0) === 0);
      case "low-fat":
        return meals.filter(meal => meal.fat && meal.fat < 10);
      case "organic":
        return meals.filter(meal => meal.dietaryTags?.includes("organic"));
      case "halal":
        return meals.filter(meal => meal.dietaryTags?.includes("halal"));
      case "pescatarian":
        return meals.filter(meal => meal.dietaryTags?.includes("pescatarian"));
      case "keto":
        return meals.filter(meal => meal.dietaryTags?.includes("keto"));
      case "plant-based":
        return meals.filter(meal => meal.dietaryTags?.includes("plant-based"));
      case "kosher":
        return meals.filter(meal => meal.dietaryTags?.includes("kosher"));
      case "climatarian":
        return meals.filter(meal => meal.dietaryTags?.includes("climatarian"));
      case "raw-food":
        return meals.filter(meal => meal.dietaryTags?.includes("raw-food"));
      case "mediterranean":
        return meals.filter(meal => meal.dietaryTags?.includes("mediterranean"));
      case "paleo":
        return meals.filter(meal => meal.dietaryTags?.includes("paleo"));
      case "kangatarian":
        return meals.filter(meal => meal.dietaryTags?.includes("kangatarian"));
      case "pollotarian":
        return meals.filter(meal => meal.dietaryTags?.includes("pollotarian"));
      case "flexitarian":
        return meals.filter(meal => meal.dietaryTags?.includes("flexitarian"));
      default:
        return meals;
    }
  };
  // Generate meal plan using backend API
  const generateMealPlanFromBackend = async () => {
    try {
      setLoading(true);

      const token = localStorage.getItem('token');
      if (!token) {
        alert('Please log in to generate meal plans');
        return;
      }

      // Calculate date range (7 days from today)
      const today = new Date();
      const endDate = new Date(today);
      endDate.setDate(today.getDate() + 6); // 7 days total

      const generateData = {
        startDate: formatDate(today),
        endDate: formatDate(endDate),
        includeFamily: true,
        calorieTarget: userBudgetPerDay || 2000 // Use budget as calorie target for now
      };

      console.log('🤖 Generating meal plan with data:', generateData);

      const response = await apiService.generateMealPlan(generateData);

      if (response.success) {
        console.log('✅ Meal plan generated successfully:', response);

        // Reload meal plans to show the generated ones
        await loadSavedMealPlans();

        alert(`✅ Generated ${response.generatedPlans} meal plans successfully!`);

        // Refresh the current view
        window.location.reload();
      } else {
        throw new Error(response.message || 'Failed to generate meal plan');
      }

    } catch (error) {
      console.error('❌ Error generating meal plan:', error);

      let errorMessage = 'Failed to generate meal plan. Please try again.';

      if (error.response?.status === 401) {
        errorMessage = 'Authentication failed. Please log in again.';
      } else if (error.response?.status === 400) {
        errorMessage = `Invalid request: ${error.response.data?.message || 'Please check your settings.'}`;
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      alert(`❌ ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  // Update the generateRandomMealPlan function to auto-save
  const generateRandomMealPlan = async (budgetPerDay, daysPerWeek) => {
    try {
      setLoading(true);

      // Fetch user dietary preferences from profile
      const userPrefsResponse = await userAPI.getDietaryPreferences();
      const userDietaryPrefs = userPrefsResponse.dietaryPreferences || {};

      // Fetch Filipino meals
      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const res = await axios.get(`${API_BASE_URL}/meals/filipino`);
      let filipinoMeals = res.data || [];

      // Filter meals by user's dietary preferences from profile
      filipinoMeals = filterMealsByUserPreferences(filipinoMeals, userDietaryPrefs);

      if (filipinoMeals.length === 0) {
        setError("No meals found matching your dietary preferences.");
        return;
      }

      // Shuffle helper
      const shuffle = arr => [...arr].sort(() => Math.random() - 0.5);

      // Get starting date (today) - make sure we're starting from today
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Normalize to midnight
      
      const mealTypesForDay = ["breakfast", "lunch", "dinner"];
      const newPlan = {};

      console.log(`Generating meal plan for ${daysPerWeek} days starting from:`, today);

      // Generate meal plan for the specified number of days
      for (let i = 0; i < daysPerWeek; i++) {
        // Create a new date object for each iteration
        const currentDate = new Date(today.getTime()); // Use getTime() to avoid reference issues
        currentDate.setDate(today.getDate() + i); // Add i days to today
        const dateStr = formatDate(currentDate);

        console.log(`Day ${i + 1}: ${dateStr} (${currentDate.toDateString()})`);

        // Initialize the day's meal plan
        newPlan[dateStr] = {
          breakfast: [],
          lunch: [],
          dinner: [],
          snack: []
        };

        // Assign meals for each meal type with budget constraints
        const budgetPerMeal = budgetPerDay / mealTypesForDay.length; // Divide budget equally among meals
        let dailyTotalCost = 0;

        mealTypesForDay.forEach((mealType, mealIndex) => {
          // Filter meals that fit within the remaining budget for this meal
          const remainingBudget = budgetPerDay - dailyTotalCost;
          const affordableMeals = filipinoMeals.filter(meal => {
            const mealPrice = meal.price || 0;
            return mealPrice <= Math.min(budgetPerMeal * 1.5, remainingBudget); // Allow 50% flexibility
          });

          if (affordableMeals.length === 0) {
            // If no affordable meals, use the cheapest available meal
            const cheapestMeal = filipinoMeals.reduce((cheapest, current) => {
              const currentPrice = current.price || 0;
              const cheapestPrice = cheapest.price || 0;
              return currentPrice < cheapestPrice ? current : cheapest;
            });
            affordableMeals.push(cheapestMeal);
          }

          // Shuffle affordable meals for variety
          const shuffledMeals = shuffle(affordableMeals);
          const meal = shuffledMeals[0]; // Pick the first from shuffled affordable meals

          if (meal) {
            const mealPrice = meal.price || 0;
            dailyTotalCost += mealPrice;

            newPlan[dateStr][mealType] = [
              {
                ...meal,
                instanceId: `${meal._id || meal.id}-${Date.now()}-${i}-${mealType}-${Math.random()}`
              }
            ];
          }
        });

        console.log(`Day ${i + 1} total cost: ₱${dailyTotalCost.toFixed(2)} (Budget: ₱${budgetPerDay})`);
      }

      console.log('Generated meal plan:', newPlan);
      console.log('Number of days generated:', Object.keys(newPlan).length);

      // Update the meal plan state - replace existing plans with new ones
      setMealPlan(newPlan);

      // Initialize rice bowls for each generated day
      const newRiceBowlsPerDay = {};
      Object.keys(newPlan).forEach(dateStr => {
        newRiceBowlsPerDay[dateStr] = defaultRiceBowls;
      });
      setRiceBowlsPerDay(newRiceBowlsPerDay);

      // Auto-save the generated meal plan
      const token = localStorage.getItem('token');
      if (token) {
        setTimeout(() => {
          const now = new Date();
          const uniqueName = `Generated Plan - ${now.toLocaleDateString()} ${now.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })}`;
          saveMealPlan(uniqueName);
        }, 1000);
      }
      
      setShowSaveSuccess(true);
      setShowMealDetails(false);
      
    } catch (err) {
      console.error("Error generating meal plan:", err);
      setError("Failed to generate meal plan. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Calculate minutes difference between two time strings (HH:MM)
  const minutesDifference = (time1, time2) => {
    const [hours1, minutes1] = time1.split(':').map(Number);
    const [hours2, minutes2] = time2.split(':').map(Number);
    const totalMinutes1 = hours1 * 60 + minutes1;
    const totalMinutes2 = hours2 * 60 + minutes2;
    return Math.abs(totalMinutes1 - totalMinutes2);
  };

  // Check for missed meal plans
  const checkForMissedMealPlans = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayStr = formatDate(today);

    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = formatDate(yesterday);

    if (mealPlan[yesterdayStr] && !lockedDates[yesterdayStr]) {
      setMissedMealNotification({
        date: yesterdayStr,
        message: `You missed completing your meal plan for ${formatDisplayDate(yesterdayStr)}`
      });
    }
  };

  // Format date to YYYY-MM-DD
  const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const formatted = `${year}-${month}-${day}`;
    // console.log('formatDate input:', date, 'output:', formatted);
    return formatted;
  };

  // Format date for display
  const formatDisplayDate = (dateStr) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Check if date is today
  const isToday = (dateStr) => {
    const today = new Date();
    const date = new Date(dateStr);
    return date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear();
  };

  // Check if date is in the past
  const isPastDate = (dateStr) => {
    return new Date(dateStr) < new Date(new Date().setHours(0, 0, 0, 0));
  };

  // Check if date is locked
  const isDateLocked = (dateStr) => {
    return lockedDates[dateStr] || false;
  };

  // Check if meal is completed
  const isMealCompleted = (date, mealType, mealInstanceId) => {
    if (!completedMeals[date]) return false;
    if (!completedMeals[date][mealType]) return false;
    return completedMeals[date][mealType].includes(mealInstanceId);
  };

  // Mark meal as completed
  const markMealCompleted = (date, mealType, mealInstanceId, isCompleted) => {
    setCompletedMeals(prev => {
      const updated = { ...prev };
      if (!updated[date]) updated[date] = {};
      if (!updated[date][mealType]) updated[date][mealType] = [];

      if (isCompleted) {
        if (!updated[date][mealType].includes(mealInstanceId)) {
          updated[date][mealType] = [...updated[date][mealType], mealInstanceId];
        }
      } else {
        updated[date][mealType] = updated[date][mealType].filter(id => id !== mealInstanceId);
      }

      return updated;
    });
    // Optionally: call API to update completion status
    // try { apiService.markMealCompleted(date, mealType, mealInstanceId, isCompleted); } catch (error) {}
  };

  // Generate calendar days
  const generateCalendarDays = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    const daysInMonth = getDaysInMonth(year, month);
    const firstDayOfMonth = getFirstDayOfMonth(year, month);

    const days = [];

    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(null);
    }

    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      days.push(formatDate(date));
    }

    return days;
  };

  // Filter meals based on dietary preference and search term (for selector)
  const getFilteredMeals = () => {
    if (loading) return [];
    if (error) return [];
    if (!meals || meals.length === 0) return [];

    let filtered = [...meals];

    if (dietaryPreference !== "all") {
      filtered = filterMealsByPreference(filtered, dietaryPreference);
    }

    if (searchTerm.trim() !== "") {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(meal =>
        meal.name.toLowerCase().includes(term) ||
        (meal.category && meal.category.toLowerCase().includes(term))
      );
    }

    return filtered;
  };

  // Add meal to plan
  const addMealToPlan = (meal) => {
    if (isDateLocked(selectedDate)) {
      alert("This meal plan is locked. Unlock it to make changes.");
      setShowMealSelector(false);
      return;
    }

    setMealPlan(prevPlan => {
      const mealInstanceId = `${meal._id || 'temp-id'}-${Date.now()}`;
      const updatedPlan = JSON.parse(JSON.stringify(prevPlan));

      if (!updatedPlan[selectedDate]) {
        updatedPlan[selectedDate] = {
          breakfast: [],
          lunch: [],
          dinner: []
        };
      } else if (!updatedPlan[selectedDate][selectedMealType]) {
        updatedPlan[selectedDate][selectedMealType] = [];
      }

      // Add meal without serving size adjustments (admin side)
      updatedPlan[selectedDate][selectedMealType].push({
        ...meal,
        instanceId: mealInstanceId
      });

      return updatedPlan;
    });

    // Track meal added to meal plan
    trackMealAddedToPlan(meal, selectedDate, selectedMealType);

    // Track analytics
    analyticsService.trackMealPlanAction('add_meal', {
      mealName: meal.name,
      mealType: selectedMealType,
      date: selectedDate,
      calories: meal.calories || 0,
      category: meal.category
    });

    // Save to backend API
    try {
      const mealPlanData = {
        date: selectedDate,
        mealType: selectedMealType,
        meal: meal,
        riceBowls: riceBowlsPerDay[selectedDate] || 0
      };

      console.log('Sending meal to backend:', mealPlanData);

      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      axios.post(`${API_BASE_URL}/meal-plans`, mealPlanData, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      }).then(response => {
        console.log('✅ Meal saved to backend successfully:', response.data);
      }).catch(error => {
        console.error('❌ Error saving meal to backend:', error.response?.data || error.message);
      });
    } catch (error) {
      console.error('❌ Error preparing meal data for backend:', error);
    }

    setShowMealSelector(false);
  };

  // Open meal details modal from meal selector
  const openMealDetailsFromSelector = (meal) => {
    setSelectedMealForDetails(meal);
    setShowMealDetailsModal(true);

    // Track meal view analytics
    analyticsService.trackMealView(meal._id || meal.id, meal.name);
  };

  // Close meal details modal
  const closeMealDetailsModal = () => {
    setShowMealDetailsModal(false);
    setSelectedMealForDetails(null);
    setServingSize(1); // Reset serving size when closing modal
  };

  // Serving size functions
  const incrementServingSize = () => {
    setServingSize(prev => Math.min(prev + 1, 10)); // Max 10 servings
  };

  const decrementServingSize = () => {
    setServingSize(prev => Math.max(prev - 1, 1)); // Min 1 serving
  };

  // Calculate adjusted nutrition values based on serving size
  const getAdjustedNutritionValue = (value, unit = '') => {
    if (!value || value === 'N/A') return 'N/A';
    const numericValue = parseFloat(value);
    if (isNaN(numericValue)) return value;
    const adjustedValue = Math.round(numericValue * servingSize);
    return unit ? `${adjustedValue}${unit}` : adjustedValue;
  };

  // Parse ingredient quantity and adjust based on serving size
  const getAdjustedIngredient = (ingredient) => {
    if (!ingredient) return ingredient;

    // Common patterns for ingredient quantities
    const patterns = [
      /^(\d+(?:\.\d+)?)\s*(\w+)?\s+(.+)$/,  // "2 cups flour" or "1.5 tbsp oil"
      /^(\d+(?:\.\d+)?)\s*(.+)$/,           // "2 eggs" or "1.5 onion"
      /^(\d+\/\d+)\s*(\w+)?\s+(.+)$/,       // "1/2 cup sugar"
      /^(\d+)\s*-\s*(\d+)\s*(\w+)?\s+(.+)$/ // "2-3 cloves garlic"
    ];

    for (const pattern of patterns) {
      const match = ingredient.match(pattern);
      if (match) {
        let quantity = match[1];
        let unit = match[2] || '';
        let item = match[3] || match[2] || '';

        // Handle fractions
        if (quantity.includes('/')) {
          const [num, den] = quantity.split('/').map(Number);
          quantity = num / den;
        } else {
          quantity = parseFloat(quantity);
        }

        if (!isNaN(quantity)) {
          const adjustedQuantity = quantity * servingSize;

          // Format the adjusted quantity nicely
          let formattedQuantity;
          if (adjustedQuantity % 1 === 0) {
            formattedQuantity = adjustedQuantity.toString();
          } else if (adjustedQuantity < 1) {
            // Convert to fraction if less than 1
            const fraction = adjustedQuantity;
            if (fraction === 0.5) formattedQuantity = '1/2';
            else if (fraction === 0.25) formattedQuantity = '1/4';
            else if (fraction === 0.75) formattedQuantity = '3/4';
            else if (fraction === 0.33) formattedQuantity = '1/3';
            else if (fraction === 0.67) formattedQuantity = '2/3';
            else formattedQuantity = adjustedQuantity.toFixed(2);
          } else {
            formattedQuantity = adjustedQuantity.toFixed(1).replace('.0', '');
          }

          return `${formattedQuantity}${unit ? ' ' + unit : ''} ${item}`.trim();
        }
      }
    }

    // If no pattern matches, return original ingredient
    return ingredient;
  };

  // Meal card serving size functions
  const getMealCardServingSize = (mealId) => {
    return mealCardServingSizes[mealId] || 1;
  };

  const incrementMealCardServingSize = (mealId) => {
    setMealCardServingSizes(prev => ({
      ...prev,
      [mealId]: Math.min((prev[mealId] || 1) + 1, 10)
    }));
  };

  const decrementMealCardServingSize = (mealId) => {
    setMealCardServingSizes(prev => ({
      ...prev,
      [mealId]: Math.max((prev[mealId] || 1) - 1, 1)
    }));
  };

  // Calculate adjusted values for meal cards
  const getAdjustedMealCardValue = (value, mealId, unit = '') => {
    if (!value || value === 'N/A') return 'N/A';
    const numericValue = parseFloat(value);
    if (isNaN(numericValue)) return value;
    const servingSize = getMealCardServingSize(mealId);
    const adjustedValue = Math.round(numericValue * servingSize);
    return unit ? `${adjustedValue}${unit}` : adjustedValue;
  };

  // Adjust ingredient for meal card display
  const getAdjustedIngredientForCard = (ingredient, mealId) => {
    if (!ingredient) return ingredient;

    const servingSize = getMealCardServingSize(mealId);

    // Common patterns for ingredient quantities
    const patterns = [
      /^(\d+(?:\.\d+)?)\s*(\w+)?\s+(.+)$/,  // "2 cups flour" or "1.5 tbsp oil"
      /^(\d+(?:\.\d+)?)\s*(.+)$/,           // "2 eggs" or "1.5 onion"
      /^(\d+\/\d+)\s*(\w+)?\s+(.+)$/,       // "1/2 cup sugar"
      /^(\d+)\s*-\s*(\d+)\s*(\w+)?\s+(.+)$/ // "2-3 cloves garlic"
    ];

    for (const pattern of patterns) {
      const match = ingredient.match(pattern);
      if (match) {
        let quantity = match[1];
        let unit = match[2] || '';
        let item = match[3] || match[2] || '';

        // Handle fractions
        if (quantity.includes('/')) {
          const [num, den] = quantity.split('/').map(Number);
          quantity = num / den;
        } else {
          quantity = parseFloat(quantity);
        }

        if (!isNaN(quantity)) {
          const adjustedQuantity = quantity * servingSize;

          // Format the adjusted quantity nicely
          let formattedQuantity;
          if (adjustedQuantity % 1 === 0) {
            formattedQuantity = adjustedQuantity.toString();
          } else if (adjustedQuantity < 1) {
            // Convert to fraction if less than 1
            const fraction = adjustedQuantity;
            if (fraction === 0.5) formattedQuantity = '1/2';
            else if (fraction === 0.25) formattedQuantity = '1/4';
            else if (fraction === 0.75) formattedQuantity = '3/4';
            else if (fraction === 0.33) formattedQuantity = '1/3';
            else if (fraction === 0.67) formattedQuantity = '2/3';
            else formattedQuantity = adjustedQuantity.toFixed(2);
          } else {
            formattedQuantity = adjustedQuantity.toFixed(1).replace('.0', '');
          }

          return `${formattedQuantity}${unit ? ' ' + unit : ''} ${item}`.trim();
        }
      }
    }

    // If no pattern matches, return original ingredient
    return ingredient;
  };

  // Track meal added to meal plan
  const trackMealAddedToPlan = async (meal, addedToDate, addedToMealType) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      await axios.post(`${API_BASE_URL}/users/recently-added-to-meal-plans`, {
        meal,
        addedToDate,
        addedToMealType
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });
    } catch (error) {
      console.error('Failed to track meal added to plan:', error);
      // Don't show error to user as this is just tracking
    }
  };

  // Update scrollbar state when container scrolls
  const updateScrollbarState = () => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      const scrollLeft = container.scrollLeft;
      const maxScroll = container.scrollWidth - container.clientWidth;
      const scrollbarWidth = maxScroll > 0 ? (container.clientWidth / container.scrollWidth) * 100 : 100;

      setScrollbarState(prev => ({
        ...prev,
        scrollLeft,
        maxScroll,
        scrollbarWidth
      }));
    }
  };

  // Handle scrollbar drag start
  const handleScrollbarMouseDown = (e) => {
    e.preventDefault();
    setScrollbarState(prev => ({ ...prev, isDragging: true }));

    const handleMouseMove = (e) => {
      if (scrollContainerRef.current) {
        const scrollbarTrack = e.currentTarget.parentElement;
        const rect = scrollbarTrack.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const percentage = Math.max(0, Math.min(1, x / rect.width));
        const newScrollLeft = percentage * scrollbarState.maxScroll;

        scrollContainerRef.current.scrollLeft = newScrollLeft;
        updateScrollbarState();
      }
    };

    const handleMouseUp = () => {
      setScrollbarState(prev => ({ ...prev, isDragging: false }));
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // Handle scrollbar track click
  const handleScrollbarTrackClick = (e) => {
    if (scrollContainerRef.current && e.target.classList.contains('custom-scrollbar-track')) {
      const rect = e.currentTarget.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const percentage = x / rect.width;
      const newScrollLeft = percentage * scrollbarState.maxScroll;

      scrollContainerRef.current.scrollTo({
        left: newScrollLeft,
        behavior: 'smooth'
      });
    }
  };

  // Handle container scroll
  const handleContainerScroll = () => {
    updateScrollbarState();
  };

  // Touch support for scrollbar
  const handleScrollbarTouchStart = (e) => {
    e.preventDefault();
    const touch = e.touches[0];
    setScrollbarState(prev => ({ ...prev, isDragging: true }));

    const handleTouchMove = (e) => {
      if (scrollContainerRef.current && e.touches[0]) {
        const touch = e.touches[0];
        const scrollbarTrack = e.currentTarget.parentElement;
        const rect = scrollbarTrack.getBoundingClientRect();
        const x = touch.clientX - rect.left;
        const percentage = Math.max(0, Math.min(1, x / rect.width));
        const newScrollLeft = percentage * scrollbarState.maxScroll;

        scrollContainerRef.current.scrollLeft = newScrollLeft;
        updateScrollbarState();
      }
    };

    const handleTouchEnd = () => {
      setScrollbarState(prev => ({ ...prev, isDragging: false }));
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };

    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleTouchEnd);
  };

  // Remove meal from plan
  const removeMealFromPlan = (date, mealType, mealInstanceId) => {
    if (isDateLocked(date)) {
      alert("This meal plan is locked. Unlock it to make changes.");
      return;
    }

    setMealPlan(prevPlan => {
      if (!prevPlan[date] || !prevPlan[date][mealType]) return prevPlan;

      const updatedPlan = { ...prevPlan };
      updatedPlan[date][mealType] = updatedPlan[date][mealType].filter(
        meal => meal.instanceId !== mealInstanceId
      );

      return updatedPlan;
    });

    // Optionally: call API to update meal plan
    // try { apiService.removeMealFromPlan(date, mealType, mealInstanceId); } catch (error) {}
  };

  // Open meal selector
  const openMealSelector = (date, mealType) => {
    if (isPastDate(date)) {
      alert("Cannot add meals to past dates");
      return;
    }
    if (isDateLocked(date)) {
      alert("This meal plan is locked. Unlock it to make changes.");
      return;
    }

    setSelectedDate(date);
    setSelectedMealType(mealType);
    setShowMealSelector(true);
  };

  // Save/lock meal plan for a specific date with AI validation and API call
  const saveMealPlanForDate = async () => {
    try {
      setLoading(true);
      console.log('🔒 Starting save & lock process for date:', selectedDate);

      // Get all meals for the selected date
      const dayMeals = mealPlan[selectedDate];
      if (!dayMeals || Object.keys(dayMeals).length === 0) {
        alert('No meals found for this date. Please add meals before saving.');
        setLoading(false);
        return;
      }

      console.log('🔍 Debug: dayMeals structure:', dayMeals);

      // Prepare meals for validation (flat format for AI analysis)
      const selectedMealsForValidation = [];
      ['breakfast', 'lunch', 'dinner', 'snack'].forEach(mealType => {
        if (dayMeals[mealType] && dayMeals[mealType].length > 0) {
          dayMeals[mealType].forEach(meal => {
            selectedMealsForValidation.push({
              mealId: meal._id || meal.id,
              name: meal.name,
              mealType: mealType,
              calories: meal.calories || 0,
              ingredients: meal.ingredients || [],
              allergens: meal.allergens || [],
              dietType: meal.dietType || 'general',
              category: meal.category || ['General'],
              description: meal.description || '',
              image: meal.image || '',
              instructions: meal.instructions || [],
              dietaryTags: meal.dietaryTags || [],
              rating: meal.rating || 0
            });
          });
        }
      });

      if (selectedMealsForValidation.length === 0) {
        alert('No meals found for this date. Please add meals before saving.');
        setLoading(false);
        return;
      }

      console.log('🍽️ Validating meals:', selectedMealsForValidation.length);

      // Skip validation temporarily and proceed directly to save
      console.log('🔍 Skipping validation for now, proceeding directly to save and lock...');
      await proceedWithSaveAndLock();

    } catch (error) {
      console.error('❌ Error saving meal plan:', error);
      alert(`Failed to save meal plan: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Proceed with save and lock after validation
  const proceedWithSaveAndLock = async () => {
    try {
      setLoading(true);
      setShowValidationModal(false);

      console.log('💾 Starting save and lock process for date:', selectedDate);

      // Get all meals for the selected date
      const dayMeals = mealPlan[selectedDate];
      const selectedMeals = [];
      ['breakfast', 'lunch', 'dinner', 'snack'].forEach(mealType => {
        if (dayMeals[mealType] && dayMeals[mealType].length > 0) {
          dayMeals[mealType].forEach(meal => {
            selectedMeals.push({
              date: selectedDate,
              mealType: mealType,
              mealData: {
                _id: meal._id || meal.id,
                name: meal.name,
                calories: meal.calories || 0,
                category: meal.category || ['General'],
                description: meal.description || '',
                image: meal.image || '',
                ingredients: meal.ingredients || [],
                instructions: meal.instructions || [],
                dietaryTags: meal.dietaryTags || [],
                rating: meal.rating || 0,
                instanceId: meal.instanceId || `${meal.name}-${Date.now()}`
              }
            });
          });
        }
      });

      // Prepare meal plan data for saving
      const mealPlanData = {
        name: `Meal Plan for ${new Date(selectedDate).toLocaleDateString()}`,
        startDate: selectedDate,
        endDate: selectedDate,
        dietaryPreference: 'all',
        riceBowls: riceBowlsPerDay[selectedDate] || 0,
        riceBowlsPerDay: { [selectedDate]: riceBowlsPerDay[selectedDate] || 0 },
        meals: selectedMeals,
        mealTimes: {
          breakfast: '08:00',
          lunch: '12:00',
          dinner: '18:00',
          snack: '15:00'
        }
      };

      console.log('💾 Saving meal plan data:', mealPlanData);
      console.log('🔍 Detailed meal plan structure:', {
        name: mealPlanData.name,
        startDate: mealPlanData.startDate,
        endDate: mealPlanData.endDate,
        mealsCount: mealPlanData.meals?.length,
        firstMeal: mealPlanData.meals?.[0],
        mealStructure: mealPlanData.meals?.[0] ? Object.keys(mealPlanData.meals[0]) : [],
        mealDataStructure: mealPlanData.meals?.[0]?.mealData ? Object.keys(mealPlanData.meals[0].mealData) : []
      });

      // Check authentication before saving
      const token = localStorage.getItem('token');
      if (!token) {
        alert('Please log in again to save meal plans');
        return;
      }

      console.log('🔐 Token check:', {
        hasToken: !!token,
        tokenLength: token.length,
        tokenStart: token.substring(0, 20) + '...'
      });

      // Validate data before sending
      if (!mealPlanData.name || !mealPlanData.startDate || !mealPlanData.endDate) {
        alert('❌ Missing required fields: name, startDate, or endDate');
        return;
      }

      if (!mealPlanData.meals || mealPlanData.meals.length === 0) {
        alert('❌ No meals to save. Please add some meals first.');
        return;
      }

      // Validate meal structure
      const invalidMeals = mealPlanData.meals.filter(meal =>
        !meal.date || !meal.mealType || !meal.mealData || !meal.mealData.name
      );

      if (invalidMeals.length > 0) {
        console.error('❌ Invalid meal data found:', invalidMeals);
        alert('❌ Some meals have invalid data. Please check the meal plan.');
        return;
      }

      console.log('✅ Data validation passed');

      // Save meal plan to backend
      const saveResponse = await apiService.saveMealPlan(mealPlanData);

      if (saveResponse.success) {
        console.log('✅ Meal plan saved successfully');

        // Lock the meal plan
        await apiService.toggleLockMealPlan(selectedDate, true);
        console.log('🔒 Meal plan locked successfully');

        // Update local state
        setLockedDates(prev => ({
          ...prev,
          [selectedDate]: true
        }));

        setEditMode(false);
        setShowSaveSuccess(true);
        setValidationResult(null);

        // Show success message
        alert('✅ Meal plan saved and locked successfully!');
      } else {
        throw new Error(saveResponse.message || 'Failed to save meal plan');
      }

    } catch (error) {
      console.error('❌ Error saving meal plan:', error);

      // Provide more specific error messages
      let errorMessage = 'Failed to save meal plan';

      if (error.status === 400) {
        errorMessage = `Validation Error: ${error.message || 'Invalid data provided'}`;
        console.error('📋 Validation details:', error.data);
      } else if (error.status === 401) {
        errorMessage = 'Authentication failed. Please log in again.';
        // Redirect to login
        window.location.href = '/login';
      } else if (error.status === 403) {
        errorMessage = 'Access denied. Please check your account status.';
      } else if (error.status === 500) {
        errorMessage = 'Server error. Please try again later.';
      } else {
        errorMessage = error.message || 'Unknown error occurred';
      }

      alert(`❌ ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  // Toggle edit mode for a specific date
  const toggleEditMode = () => {
    const newLockStatus = !isDateLocked(selectedDate);
    if (isDateLocked(selectedDate)) {
      setLockedDates(prev => {
        const updated = { ...prev };
        delete updated[selectedDate];
        return updated;
      });
    } else {
      setLockedDates(prev => ({
        ...prev,
        [selectedDate]: true
      }));
    }

    // Optionally: call API to update lock status
    // try { apiService.toggleLockMealPlan(selectedDate, newLockStatus); } catch (error) {}
    setEditMode(!editMode);
  };

  // Calculate calories for a specific meal type on a specific date
  const calculateMealTypeCalories = (date, mealType) => {
    if (!mealPlan[date] || !mealPlan[date][mealType]) return 0;
    return mealPlan[date][mealType].reduce((total, meal) => {
      return total + (meal.calories || 0);
    }, 0);
  };

  // Calculate total calories for a day
  const calculateDailyCalories = (date) => {
    if (!mealPlan[date]) return 0;
    return mealTypes.reduce((total, mealType) => {
      return total + calculateMealTypeCalories(date, mealType);
    }, 0);
  };

  // Get meal count for a date
  const getMealCount = (date) => {
    if (!mealPlan[date]) {
      // console.log(`No meal plan for date: ${date}`);
      return 0;
    }

    const count = mealTypes.reduce((count, mealType) => {
      const mealTypeCount = mealPlan[date][mealType] ? mealPlan[date][mealType].length : 0;
      return count + mealTypeCount;
    }, 0);

    // console.log(`Meal count for ${date}:`, count, mealPlan[date]);
    return count;
  };

  // Handle date selection
  const handleDateClick = (date) => {
    if (!date) return;
    if (isPastDate(date)) {
      alert("You cannot set or edit a meal plan for past dates.");
      return;
    }
    setSelectedDate(date);
    setShowMealDetails(true);
    setEditMode(!isDateLocked(date));
  };

  // Get month name
  const getMonthName = (date) => {
    return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  };

  // Update meal time
  const updateMealTime = (mealType, time) => {
    setMealTimes(prev => ({
      ...prev,
      [mealType]: time
    }));
  };

  // Dismiss meal time alert
  const dismissMealAlert = (date, mealType) => {
    setMissedMealAlerts(prev =>
      prev.filter(alert => !(alert.date === date && alert.mealType === mealType))
    );
    if (missedMealAlerts.length <= 1) {
      setShowMissedMealAlert(false);
    }
  };

  // Check if a meal is missed (past its scheduled time)
  const isMealMissed = (date, mealType) => {
    if (isPastDate(date)) return true;
    if (isToday(date)) {
      const now = new Date();
      const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
      return currentTime > mealTimes[mealType];
    }
    return false;
  };

  // Handle mouse enter for tooltip
  const handleMouseEnter = (date, event) => {
    if (!date) return;

    // Clear any existing timeout
    if (tooltipTimeout) {
      clearTimeout(tooltipTimeout);
    }

    const rect = event.currentTarget.getBoundingClientRect();
    setTooltipPosition({
      x: rect.left + rect.width / 2,
      y: rect.top - 10
    });

    // Add a small delay to prevent flickering
    const timeout = setTimeout(() => {
      setHoveredDate(date);
    }, 200);

    setTooltipTimeout(timeout);
  };

  // Handle mouse leave for tooltip
  const handleMouseLeave = () => {
    if (tooltipTimeout) {
      clearTimeout(tooltipTimeout);
      setTooltipTimeout(null);
    }
    setHoveredDate(null);
  };

  // Get meals for tooltip
  const getMealsForTooltip = (date) => {
    if (!mealPlan[date]) return [];

    const meals = [];
    mealTypes.forEach(mealType => {
      if (mealPlan[date][mealType] && mealPlan[date][mealType].length > 0) {
        mealPlan[date][mealType].forEach(meal => {
          meals.push({
            type: mealType,
            name: meal.name,
            calories: meal.calories
          });
        });
      }
    });
    return meals;
  };

  const calendarDays = generateCalendarDays();
  // Loading state
  if (loading) {
    return (
      <Layout>
        <div className="main-content">
          <div className="container">
            <h1>MEAL PLAN CALENDAR</h1>
            <div className="loading-state">
              <div className="circle-loader large primary"></div>
              <p>Loading meals...</p>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  // Error state
  if (error) {
    return (
      <Layout>
        <div className="main-content">
          <div className="container">
            <h1>MEAL PLAN CALENDAR</h1>
            <div className="error-container">
              <p>{error}</p>
              <button onClick={() => window.location.reload()}>Retry</button>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      {showPreferencesModal && (
        <div className="preferences-modal-overlay">
          <div className="preferences-modal">
            <div className="modal-header">
              <h2><FaCog /> Meal Plan Preferences</h2>
              <button
                className="close-btn"
                onClick={() => setShowPreferencesModal(false)}
                type="button"
              >
                <FaTimes />
              </button>
            </div>

            <div className="modal-body">
              <p className="preferences-description">
                Set your daily budget and meal plan duration. Your dietary preferences from your profile will be automatically applied. You can generate meal plans manually using the "Generate Plan" button.
              </p>

              <form
                onSubmit={async e => {
                  e.preventDefault();
                  setUserBudgetPerDay(userBudgetPerDay);
                  setUserMealDaysPerWeek(userMealDaysPerWeek);
                  localStorage.setItem(
                    'mealPlanUserPrefs',
                    JSON.stringify({
                      budgetPerDay: userBudgetPerDay,
                      mealDaysPerWeek: userMealDaysPerWeek,
                      defaultRiceBowls: defaultRiceBowls,
                    })
                  );
                  setShowPreferencesModal(false);
                  // Removed automatic meal plan generation - users can manually generate if needed
                }}
              >
                <div className="preferences-grid">
                  <div className="form-group">
                    <label htmlFor="budget-per-day">
                      💰 Daily Budget (₱)
                    </label>
                    <input
                      id="budget-per-day"
                      type="number"
                      min={100}
                      max={2000}
                      step={50}
                      value={userBudgetPerDay}
                      onChange={e => setUserBudgetPerDay(Number(e.target.value))}
                    />
                    <small className="form-help">Set your daily budget for meals (in Philippine Pesos)</small>
                  </div>

                  <div className="form-group">
                    <label htmlFor="days-per-week">
                      <FaCalendarAlt /> Days per Week
                    </label>
                    <input
                      id="days-per-week"
                      type="number"
                      min={1}
                      max={7}
                      value={userMealDaysPerWeek}
                      onChange={e => setUserMealDaysPerWeek(Number(e.target.value))}
                    />
                    <small className="form-help">How many days should your meal plan cover?</small>
                  </div>

                  <div className="form-group">
                    <label htmlFor="rice-bowls">
                      🍚 Rice Bowls per Day
                    </label>
                    <input
                      id="rice-bowls"
                      type="number"
                      min={0}
                      max={10}
                      value={defaultRiceBowls}
                      onChange={e => setDefaultRiceBowls(Number(e.target.value))}
                    />
                    <small className="form-help">Default number of rice bowls to include with meals</small>
                  </div>
                </div>

                <div className="modal-actions">
                  <button
                    type="button"
                    className="cancel-btn"
                    onClick={() => setShowPreferencesModal(false)}
                  >
                    Cancel
                  </button>
                  <button type="submit" className="save-btn">
                    <FaUtensils /> Generate Meal Plan
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      <div className="main-content">
          {/* Header Section */}
          <div className="meal-plan-header-section">
            <div className="header-title">
              <h1><FaUtensils /> MEAL PLAN CALENDAR</h1>
              <p className="header-subtitle">Help your family eat better, stay on track, and live a healthier life together.</p>
            </div>
            {/* Quick Actions */}
            <div className="header-actions">
              <button
                onClick={() => setShowPreferencesModal(true)}
                className="preferences-btn"
                title="Dietary Preferences"
              >
                <FaCog /> Preferences
              </button>
              <button
                onClick={() => generateMealPlanFromBackend()}
                className="generate-plan-btn"
                title="Generate New Plan"
                disabled={loading}
              >
                <FaUtensils /> {loading ? 'Generating...' : 'Generate Plan'}
              </button>
            </div>
          </div>

          {/* Dietary Preferences Indicator */}
          {userPreferences && (userPreferences.restrictions?.length > 0 || userPreferences.allergies?.length > 0) && (
            <div className="dietary-info-section">
              <div className="dietary-info-header">
                <span>🍃 Active Dietary Filters:</span>
                <button
                  className="edit-preferences-btn"
                  onClick={() => window.location.href = '/dietary-preferences'}
                >
                  Edit Preferences
                </button>
              </div>
              <div className="dietary-info-content">
                {userPreferences.restrictions?.length > 0 && (
                  <div className="dietary-info-item">
                    <span className="dietary-label">Restrictions:</span>
                    <span className="dietary-values">{userPreferences.restrictions.join(', ')}</span>
                  </div>
                )}
                {userPreferences.allergies?.length > 0 && (
                  <div className="dietary-info-item">
                    <span className="dietary-label">Allergies:</span>
                    <span className="dietary-values">{userPreferences.allergies.join(', ')}</span>
                  </div>
                )}
              </div>
            </div>
          )}
          {/* Notifications Section */}
          <div className="notifications-section">
            {/* Missed Meal Plan Notification */}
            {missedMealNotification && (
              <div className="missed-plan-notification">
                <FaExclamationTriangle className="warning-icon" />
                <p>{missedMealNotification.message}</p>
                <button
                  onClick={() => {
                    handleDateClick(missedMealNotification.date);
                    setMissedMealNotification(null);
                  }}
                  className="view-missed-plan-btn"
                >
                  View Plan
                </button>
                <button
                  onClick={() => setMissedMealNotification(null)}
                  className="dismiss-btn"
                >
                  Dismiss
                </button>
              </div>
            )}

            {/* Missed Meal Alerts */}
            {showMissedMealAlert && missedMealAlerts.length > 0 && (
              <div className="missed-meal-alert">
                <FaBell className="alert-icon" />
                <div>
                  <p>
                    {missedMealAlerts.map(alert => (
                      <span key={alert.date + alert.mealType}>
                        Missed {alert.mealType} on {formatDisplayDate(alert.date)} at {alert.time}
                        <button
                          className="dismiss-btn"
                          onClick={() => dismissMealAlert(alert.date, alert.mealType)}
                        >
                          <FaTimes />
                        </button>
                        <br />
                      </span>
                    ))}
                  </p>
                </div>
              </div>
            )}

            {/* Success message */}
            {showSaveSuccess && (
              <div className="save-success-message">
                <FaCheck/> Meal plan saved!
              </div>
            )}
          </div>

          {/* Calendar Navigation */}
          <div className="calendar-controls">
            <button onClick={goToPreviousMonth}>
              <FaArrowLeft /> Previous
            </button>
            <span className="month-label">{getMonthName(currentMonth)}</span>
            <button onClick={goToNextMonth}>
              Next <FaArrowRight />
            </button>
            <button onClick={goToCurrentMonth}>
              <FaCalendarAlt /> Today
            </button>
          </div>

          {/* Add this to your JSX where you want the save button */}
          {(() => {
            // Check if there are actually meals planned, not just empty date objects
            const hasActualMeals = Object.keys(mealPlan).some(date => {
              const dayMeals = mealPlan[date];
              if (!dayMeals || typeof dayMeals !== 'object') return false;
              return Object.values(dayMeals).some(meals =>
                Array.isArray(meals) && meals.length > 0
              );
            });
            return hasActualMeals;
          })() && (
           <div className="meal-plan-actions">
     <button
    onClick={openSaveMealPlanModal}
    className="save-plan-btn"
     >
    <FaSave /> Save Meal Plan
  </button>
</div>

   )}


   {/* Calendar - Full Width */}
        <div className="calendar-grid-full-width">
          <div className="calendar-grid">
            <div className="calendar-header">
              <div>Sun</div>
              <div>Mon</div>
              <div>Tue</div>
              <div>Wed</div>
              <div>Thu</div>
              <div>Fri</div>
              <div>Sat</div>
            </div>
            <div className="calendar-days">
              {calendarDays.map((date, idx) => (
                <div
                  key={idx}
                  className={`calendar-day${date ? '' : ' empty'}${date === selectedDate ? ' selected' : ''}${isToday(date) ? ' today' : ''}${isDateLocked(date) ? ' locked' : ''}${isPastDate(date) ? ' past' : ''}`}
                  onClick={() => date && !isPastDate(date) && handleDateClick(date)}
                  onMouseEnter={(e) => date && handleMouseEnter(date, e)}
                  onMouseLeave={handleMouseLeave}
                  style={isPastDate(date) ? { pointerEvents: 'none', opacity: 0.5, cursor: 'not-allowed' } : {}}
                >
                  {date && (
                    <>
                      <div className="day-header">
                        <span className="day-number">{new Date(date).getDate()}</span>
                        {isDateLocked(date) && <FaLock className="lock-icon" />}
                      </div>

                      <div className="meal-indicators">
                        {mealTypes.map(mealType => {
                          const mealCount = mealPlan[date] && mealPlan[date][mealType] ? mealPlan[date][mealType].length : 0;
                          return (
                            <div key={mealType} className={`meal-indicator ${mealType} ${mealCount > 0 ? 'has-meals' : ''}`}>
                              <span className="meal-type-initial">{mealType.charAt(0).toUpperCase()}</span>
                              {mealCount > 0 && <span className="meal-count-badge">{mealCount}</span>}
                            </div>
                          );
                        })}
                      </div>

                      <div className="day-summary">
                        <span className="total-meals">{getMealCount(date)} meals</span>
                        <span className="total-calories">{calculateDailyCalories(date)} kcal</span>
                        {riceBowlsPerDay[date] > 0 && (
                          <span className="rice-bowls">🍚 {riceBowlsPerDay[date]}</span>
                        )}
                      </div>
                    </>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Meal Tooltip */}
        {hoveredDate && (
          <div
            className="meal-tooltip"
            style={{
              position: 'fixed',
              left: `${tooltipPosition.x}px`,
              top: `${tooltipPosition.y}px`,
              transform: 'translateX(-50%) translateY(-100%)',
              zIndex: 1000,
              pointerEvents: 'none'
            }}
          >
            <div className="tooltip-content">
              <h4>{formatDisplayDate(hoveredDate)}</h4>
              {getMealsForTooltip(hoveredDate).length > 0 ? (
                <div className="tooltip-meals">
                  {mealTypes.map(mealType => {
                    const mealsOfType = getMealsForTooltip(hoveredDate).filter(meal => meal.type === mealType);
                    if (mealsOfType.length === 0) return null;

                    return (
                      <div key={mealType} className="tooltip-meal-type">
                        <strong>{mealType.charAt(0).toUpperCase() + mealType.slice(1)}:</strong>
                        <ul>
                          {mealsOfType.map((meal, index) => (
                            <li key={index}>
                              {meal.name} {meal.calories && `(${meal.calories} kcal)`}
                            </li>
                          ))}
                        </ul>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <p>No meals planned</p>
              )}
            </div>
          </div>
        )}

        <div className="container">
          {/* Meal Details Modal */}
          {showMealDetails && selectedDate && (
            <div className="meal-details-modal-overlay">
              <div className="meal-details-modal">
                <div className="modal-header">
                  <h2>
                    Meal Plan for {formatDisplayDate(selectedDate)}
                    {isDateLocked(selectedDate) ? (
                      <FaLock className="lock-icon" />
                    ) : (
                      <FaUnlock className="unlock-icon" />
                    )}
                  </h2>
                  <button className="close-btn" onClick={() => setShowMealDetails(false)}>
                    <FaTimes />
                  </button>
                </div>
                <div className="modal-body">
                  {mealTypes.map(mealType => (
                    <div key={mealType} className="meal-type-section">
                      <div className="meal-type-header">
                        <h3>{mealType.charAt(0).toUpperCase() + mealType.slice(1)}</h3>
                        <span className="meal-time">
                          <FaClock />{' '}
                          <input
                            type="time"
                            value={mealTimes[mealType] || defaultMealTimes[mealType]}
                            disabled={isDateLocked(selectedDate)}
                            onChange={e => updateMealTime(mealType, e.target.value)}
                          />
                        </span>
                        {!isDateLocked(selectedDate) && (
                          <button
                            className="add-meal-btn"
                            onClick={() => openMealSelector(selectedDate, mealType)}
                          >
                            <FaPlus /> Add Meal
                          </button>
                        )}
                      </div>
                      <ul className="meal-list">
                        {mealPlan[selectedDate] &&
                          mealPlan[selectedDate][mealType] &&
                          mealPlan[selectedDate][mealType].map(meal => (
                            <li key={meal.instanceId} className="meal-item">
                              <span className="meal-name">{meal.name}</span>
                              <span className="meal-calories">
                                {meal.calories ? `${meal.calories} kcal` : ''}
                              </span>
                              <div className="meal-item-actions">
                                <input
                                  type="checkbox"
                                  checked={isMealCompleted(selectedDate, mealType, meal.instanceId)}
                                  onChange={e =>
                                    markMealCompleted(
                                      selectedDate,
                                      mealType,
                                      meal.instanceId,
                                      e.target.checked
                                    )
                                  }
                                  disabled={isDateLocked(selectedDate)}
                                  title="Mark as completed"
                                />
                                {!isDateLocked(selectedDate) && (
                                  <button
                                    className="remove-meal-btn"
                                    onClick={() =>
                                      removeMealFromPlan(selectedDate, mealType, meal.instanceId)
                                    }
                                    title="Remove meal"
                                  >
                                    <FaTrash />
                                  </button>
                                )}
                              </div>
                            </li>
                          ))}
                      </ul>
                      <div className="meal-type-calories">
                        Total: {calculateMealTypeCalories(selectedDate, mealType)} kcal
                      </div>
                    </div>
                  ))}

                  {/* Rice Bowls Section */}
                  <div className="rice-bowls-section">
                    <h3>Rice Bowls</h3>
                    <div className="rice-bowls-input">
                      <label>Number of rice bowls for this day:</label>
                      <div className="rice-input-controls">
                        <button
                          type="button"
                          onClick={() => {
                            const currentRice = riceBowlsPerDay[selectedDate] || 0;
                            const newValue = Math.max(0, currentRice - 1);
                            setRiceBowlsPerDay(prev => ({
                              ...prev,
                              [selectedDate]: newValue
                            }));
                            saveRiceBowlsToBackend(selectedDate, newValue);
                          }}
                          disabled={isDateLocked(selectedDate)}
                          className="rice-btn"
                        >
                          -
                        </button>
                        <input
                          type="number"
                          min="0"
                          value={riceBowlsPerDay[selectedDate] || 0}
                          onChange={(e) => {
                            const value = Math.max(0, parseInt(e.target.value) || 0);
                            setRiceBowlsPerDay(prev => ({
                              ...prev,
                              [selectedDate]: value
                            }));
                            saveRiceBowlsToBackend(selectedDate, value);
                          }}
                          disabled={isDateLocked(selectedDate)}
                          className="rice-input"
                        />
                        <button
                          type="button"
                          onClick={() => {
                            const currentRice = riceBowlsPerDay[selectedDate] || 0;
                            const newValue = currentRice + 1;
                            setRiceBowlsPerDay(prev => ({
                              ...prev,
                              [selectedDate]: newValue
                            }));
                            saveRiceBowlsToBackend(selectedDate, newValue);
                          }}
                          disabled={isDateLocked(selectedDate)}
                          className="rice-btn"
                        >
                          +
                        </button>
                      </div>
                    </div>
                  </div>

                  <div className="daily-calories">
                    <strong>Total Calories for the Day: {calculateDailyCalories(selectedDate)} kcal</strong>
                    {riceBowlsPerDay[selectedDate] > 0 && (
                      <div className="rice-summary">
                        Rice: {riceBowlsPerDay[selectedDate]} bowl{riceBowlsPerDay[selectedDate] !== 1 ? 's' : ''}
                      </div>
                    )}
                  </div>
                </div>
                <div className="modal-footer">
                  {!isDateLocked(selectedDate) ? (
                    <button
                      className="save-btn"
                      onClick={saveMealPlanForDate}
                      disabled={loading}
                    >
                      {loading ? (
                        <>
                          <div className="circle-loader small white"></div> Saving & Analyzing...
                        </>
                      ) : (
                        <>
                          <FaSave /> Save & Lock Plan
                        </>
                      )}
                    </button>
                  ) : (
                    <button className="edit-btn" onClick={toggleEditMode}>
                      <FaEdit /> Unlock to Edit
                    </button>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Meal Selector Modal */}
          {showMealSelector && (
            <div className="meal-selector-modal-overlay">
              <div className="meal-selector-modal">
                <div className="modal-header">
                  <h2>
                    <FaUtensils /> Select Meal for {selectedMealType.charAt(0).toUpperCase() + selectedMealType.slice(1)}
                  </h2>
                  <button className="close-btn" onClick={() => setShowMealSelector(false)}>
                    <FaTimes />
                  </button>
                </div>
                <div className="modal-body">
                  {/* Search and Filter Section */}
                  <div className="search-filter-section">
                    <div className="search-container">
                      <input
                        type="text"
                        className="meal-search"
                        placeholder="Search meals..."
                        value={searchTerm}
                        onChange={e => setSearchTerm(e.target.value)}
                      />
                    </div>
                    <div className="filter-container">
                      <select
                        value={dietaryPreference}
                        onChange={e => setDietaryPreference(e.target.value)}
                        className="dietary-filter"
                      >
                        <option value="all">All Meals</option>
                        <option value="vegetarian">Vegetarian</option>
                        <option value="vegan">Vegan</option>
                        <option value="gluten-free">Gluten-Free</option>
                        <option value="dairy-free">Dairy-Free</option>
                        <option value="low-carb">Low-Carb</option>
                        <option value="keto">Keto</option>
                      </select>
                    </div>
                  </div>

                  {/* Meals Grid */}
                  {getFilteredMeals().length > 0 ? (
                    <div className="meals-grid-container">
                      <div className="meals-grid">
                        {getFilteredMeals().map(meal => (
                          <div key={meal._id || meal.id} className="meal-card-grid">
                            <div className="meal-card-image">
                              {meal.image ? (
                                <img src={meal.image} alt={meal.name} />
                              ) : (
                                <div className="meal-placeholder">
                                  🍽️
                                </div>
                              )}
                              <button
                                className="meal-card-details-eye-btn"
                                onClick={() => openMealDetailsFromSelector(meal)}
                                title="See details"
                              >
                                <FaEye />
                                <span className="sr-only">See details</span>
                              </button>
                            </div>
                            <div className="meal-card-content">
                              <h3 className="meal-card-title">{meal.name}</h3>
                              <div className="meal-card-meta">
                                <span className="meal-card-calories">
                                  {getAdjustedMealCardValue(meal.calories, meal._id || meal.id)} kcal
                                </span>
                                {meal.category && (
                                  <span className="meal-card-category">
                                    {Array.isArray(meal.category) ? meal.category[0] : meal.category}
                                  </span>
                                )}
                              </div>
                              {meal.description && (
                                <p className="meal-card-description">
                                  {meal.description.length > 60
                                    ? `${meal.description.substring(0, 60)}...`
                                    : meal.description}
                                </p>
                              )}

                              {/* Ingredients Section */}
                              {meal.ingredients && meal.ingredients.length > 0 && (
                                <div className="meal-card-ingredients">
                                  <h4>Ingredients:</h4>
                                  <div className="ingredients-list">
                                    {meal.ingredients.slice(0, 3).map((ingredient, index) => (
                                      <div key={index} className="ingredient-item">
                                        {getAdjustedIngredientForCard(ingredient, meal._id || meal.id)}
                                      </div>
                                    ))}
                                    {meal.ingredients.length > 3 && (
                                      <div className="more-ingredients">
                                        +{meal.ingredients.length - 3} more...
                                      </div>
                                    )}
                                  </div>
                                </div>
                              )}

                              {/* Serving Size Control for Meal Card */}
                              <div className="meal-card-serving-control">
                                <div className="serving-controls">
                                  <button
                                    className="serving-btn-card decrement"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      decrementMealCardServingSize(meal._id || meal.id);
                                    }}
                                    disabled={getMealCardServingSize(meal._id || meal.id) <= 1}
                                  >
                                    -
                                  </button>
                                  <span className="serving-count-card">
                                    {getMealCardServingSize(meal._id || meal.id)}
                                  </span>
                                  <button
                                    className="serving-btn-card increment"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      incrementMealCardServingSize(meal._id || meal.id);
                                    }}
                                    disabled={getMealCardServingSize(meal._id || meal.id) >= 10}
                                  >
                                    +
                                  </button>
                                </div>
                              </div>
                              <div className="meal-card-tags">
                                {meal.prepTime && (
                                  <span className="meal-tag prep-time">
                                    <FaClock /> {meal.prepTime}min
                                  </span>
                                )}
                                {meal.rating && (
                                  <span className="meal-tag rating">
                                    ⭐ {meal.rating}
                                  </span>
                                )}
                              </div>

                              {/* AI Compatibility Analysis with Lazy Loading */}
                              {(() => {
                                const compatibility = getMealCompatibility(meal);

                                // Show pending state during lazy loading
                                if (compatibility.pending && !compatibility.familyCompatibility.length) {
                                  return (
                                    <div className="meal-compatibility-pending">
                                      <div className="circle-loader small primary"></div> Analyzing...
                                    </div>
                                  );
                                }

                                // Show results as they come in (lazy loading)
                                if (compatibility.familyCompatibility.length > 0) {
                                  return (
                                    <div className="meal-compatibility">
                                      <div className="compatibility-header">
                                        <span className={`compatibility-badge ${compatibility.overallCompatibility}`}>
                                          {compatibility.overallCompatibility === 'excellent' && '🟢'}
                                          {compatibility.overallCompatibility === 'good' && '🟡'}
                                          {compatibility.overallCompatibility === 'fair' && '🟠'}
                                          {compatibility.overallCompatibility === 'poor' && '🔴'}
                                          {compatibility.overallCompatibility === 'pending' && '⏳'}
                                          {compatibility.overallCompatibility === 'unknown' && '⚪'}
                                          {compatibility.overallCompatibility.charAt(0).toUpperCase() + compatibility.overallCompatibility.slice(1)}
                                        </span>
                                      </div>
                                      <div className="family-compatibility">
                                        {compatibility.familyCompatibility.map((member, index) => (
                                          <div key={index} className={`member-compatibility ${member.compatible ? 'compatible' : 'incompatible'}`}>
                                            <span className="member-name">{member.memberName}:</span>
                                            <span className="compatibility-icon">
                                              {member.compatible ? '✅' : '❌'}
                                            </span>
                                            <span className="concern-text">
                                              {member.reasons && member.reasons.length > 0
                                                ? member.reasons[0]
                                                : (member.compatible ? 'All is well' : 'Issue found')
                                              }
                                            </span>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  );
                                }

                                // Show nothing if no analysis yet and not loading
                                return null;
                              })()}
                            </div>
                            <div className="meal-card-actions">
                              <button
                                className="meal-card-add-btn"
                                onClick={() => addMealToPlan(meal)}
                                title="Add to meal plan"
                              >
                                <FaPlus /> Add
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="no-meals-found">
                      <FaExclamationTriangle />
                      <p>No meals found matching your search criteria.</p>
                      <p>Try adjusting your search terms or dietary preferences.</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Meal Details Modal */}
          {showMealDetailsModal && selectedMealForDetails && (
            <div className="modal-overlay">
              <div className="modal-content">
                <div className="modal-header">
                  <h2>{selectedMealForDetails.name}</h2>
                  <button className="close-modal" onClick={closeMealDetailsModal}>
                    <FaTimes />
                  </button>
                </div>
                <div className="modal-body">
                  <div className="meal-image">
                    <img src={selectedMealForDetails.image} alt={selectedMealForDetails.name} />
                  </div>
                  <div className="meal-details">
                    <p className="meal-description">
                      {selectedMealForDetails.description}
                    </p>
                    <div className="meal-meta">
                      <span className="meal-rating">
                        {selectedMealForDetails.rating} &#9733;
                      </span>
                      <span className="meal-category">
                        {selectedMealForDetails.category}
                      </span>
                      <span className="meal-price">
                        Calories: {selectedMealForDetails.calories} (
                        {selectedMealForDetails.priceRange} Range)
                      </span>
                    </div>
                    {/* Serving Size Control */}
                    <div className="serving-size-control">
                      <h3>Serving Size</h3>
                      <div className="serving-size-selector">
                        <button
                          className="serving-btn decrement"
                          onClick={decrementServingSize}
                          disabled={servingSize <= 1}
                        >
                          -
                        </button>
                        <span className="serving-size-display">{servingSize} serving{servingSize > 1 ? 's' : ''}</span>
                        <button
                          className="serving-btn increment"
                          onClick={incrementServingSize}
                          disabled={servingSize >= 10}
                        >
                          +
                        </button>
                      </div>
                    </div>

                    <div className="meal-nutrition">
                      <h3>Nutrition Information <span className="per-meal-label">(per meal)</span></h3>
                      <div className="nutrition-grid">
                        <div className="nutrition-item">
                          <span className="nutrition-label">Calories</span>
                          <span className="nutrition-value">{getAdjustedNutritionValue(selectedMealForDetails.calories)}</span>
                        </div>
                        <div className="nutrition-item">
                          <span className="nutrition-label">Protein</span>
                          <span className="nutrition-value">{getAdjustedNutritionValue(selectedMealForDetails.protein, 'g')}</span>
                        </div>
                        <div className="nutrition-item">
                          <span className="nutrition-label">Carbs</span>
                          <span className="nutrition-value">{getAdjustedNutritionValue(selectedMealForDetails.carbs, 'g')}</span>
                        </div>
                        <div className="nutrition-item">
                          <span className="nutrition-label">Fat</span>
                          <span className="nutrition-value">{getAdjustedNutritionValue(selectedMealForDetails.fat, 'g')}</span>
                        </div>
                      </div>
                    </div>
                    {selectedMealForDetails.ingredients && (
                      <div className="meal-ingredients">
                        <h3>Ingredients {servingSize > 1 && <span className="serving-note">(for {servingSize} servings)</span>}</h3>
                        <ul>
                          {selectedMealForDetails.ingredients.map((ingredient, index) => (
                            <li key={index}>{getAdjustedIngredient(ingredient)}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {selectedMealForDetails.instructions && (
                      <div className="meal-steps">
                        <h3>Instructions</h3>
                        <ol>
                          {selectedMealForDetails.instructions.map((step, index) => (
                            <li key={index}>{step}</li>
                          ))}
                        </ol>
                      </div>
                    )}
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    className="meal-card-add-btn"
                    onClick={() => {
                      addMealToPlan(selectedMealForDetails);
                      closeMealDetailsModal();
                    }}
                    title="Add to meal plan"
                  >
                    <FaPlus /> Add to Plan
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Validation Modal */}
      {showValidationModal && (
        <div className="validation-modal-overlay">
          <div className="validation-modal">
            <div className="modal-header">
              <h2>🤖 Meal Plan Analysis</h2>
              <button
                className="close-btn"
                onClick={() => setShowValidationModal(false)}
                type="button"
              >
                <FaTimes />
              </button>
            </div>

            <div className="modal-body">
              {validationLoading ? (
                <div className="validation-loading">
                  <div className="circle-loader large primary"></div>
                  <p>Analyzing your meal plan...</p>
                </div>
              ) : validationResult ? (
                <div className="validation-results">
                  <div className="validation-summary">
                    <h3>📊 Summary</h3>
                    <p><strong>Total Calories:</strong> {validationResult.totalCalories} kcal</p>
                  </div>

                  <div className="validation-members">
                    <h3>👥 Family Analysis</h3>
                    {validationResult.memberValidations.map((member, index) => (
                      <div key={index} className={`validation-member-card ${member.concern === 'All is well' ? 'good' : 'warning'}`}>
                        <div className="member-header">
                          <span className="member-name">{member.memberName}</span>
                          <span className={`status-badge ${member.concern === 'All is well' ? 'good' : 'warning'}`}>
                            {member.concern === 'All is well' ? '✓' : '⚠️'}
                          </span>
                        </div>
                        <p className="member-concern">{member.concern}</p>
                      </div>
                    ))}
                  </div>
                </div>
              ) : null}
            </div>

            <div className="modal-footer">
              {validationResult?.hasIssues ? (
                <>
                  <button
                    className="btn-secondary"
                    onClick={() => setShowValidationModal(false)}
                    type="button"
                  >
                    Change Meals
                  </button>
                  <button
                    className="btn-primary"
                    onClick={proceedWithSaveAndLock}
                    type="button"
                    disabled={loading}
                  >
                    {loading ? 'Saving...' : 'Save & Lock Anyway'}
                  </button>
                </>
              ) : (
                <>
                  <button
                    className="btn-secondary"
                    onClick={() => setShowValidationModal(false)}
                    type="button"
                  >
                    Cancel
                  </button>
                  <button
                    className="btn-primary"
                    onClick={proceedWithSaveAndLock}
                    type="button"
                    disabled={loading}
                  >
                    {loading ? 'Saving...' : 'Save & Lock Plan'}
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Save Meal Plan Modal */}
      {showSaveMealPlanModal && (
        <div className="modal-overlay" onClick={closeSaveMealPlanModal}>
          <div className="modal-content save-meal-plan-modal" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>Save Meal Plan</h2>
              <button className="close-btn" onClick={closeSaveMealPlanModal}>
                <FaTimes />
              </button>
            </div>

            <div className="modal-body">
              <div className="save-plan-form">
                {/* Plan Name Input */}
                <div className="form-group">
                  <label htmlFor="planName">Meal Plan Name *</label>
                  <input
                    type="text"
                    id="planName"
                    value={savePlanName}
                    onChange={(e) => setSavePlanName(e.target.value)}
                    placeholder="Enter a name for your meal plan"
                    className="form-input"
                    maxLength={50}
                  />
                  <small className="form-hint">Choose a descriptive name for your meal plan</small>
                </div>

                {/* Date Range Selection */}
                <div className="date-range-section">
                  <h3>Select Date Range</h3>
                  <div className="date-inputs">
                    <div className="form-group">
                      <label htmlFor="startDate">Start Date *</label>
                      <input
                        type="date"
                        id="startDate"
                        value={savePlanStartDate}
                        onChange={(e) => setSavePlanStartDate(e.target.value)}
                        className="form-input"
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="endDate">End Date *</label>
                      <input
                        type="date"
                        id="endDate"
                        value={savePlanEndDate}
                        onChange={(e) => setSavePlanEndDate(e.target.value)}
                        min={savePlanStartDate}
                        className="form-input"
                      />
                    </div>
                  </div>
                </div>

                {/* Preview Section */}
                {savePlanPreview && (
                  <div className="meal-plan-preview">
                    <h3>Preview</h3>
                    <div className="preview-summary">
                      <div className="preview-stats">
                        <div className="stat-item">
                          <span className="stat-label">Date Range:</span>
                          <span className="stat-value">{savePlanPreview.dateRange}</span>
                        </div>
                        <div className="stat-item">
                          <span className="stat-label">Total Days:</span>
                          <span className="stat-value">{savePlanPreview.totalDays}</span>
                        </div>
                        <div className="stat-item">
                          <span className="stat-label">Days with Meals:</span>
                          <span className="stat-value">{savePlanPreview.daysWithMeals}</span>
                        </div>
                        <div className="stat-item">
                          <span className="stat-label">Total Meals:</span>
                          <span className="stat-value">{savePlanPreview.totalMeals}</span>
                        </div>
                      </div>

                      {savePlanPreview.daysWithMeals > 0 && (
                        <div className="preview-details">
                          <h4>Meals by Date</h4>
                          <div className="meals-by-date">
                            {Object.entries(savePlanPreview.mealsByDate).map(([date, meals]) => (
                              <div key={date} className="date-meal-summary">
                                <div className="date-header">
                                  <span className="date-label">{formatDisplayDate(date)}</span>
                                  <span className="meal-count">{meals.total} meals</span>
                                </div>
                                <div className="meal-types">
                                  {meals.breakfast > 0 && <span className="meal-type">🍳 {meals.breakfast}</span>}
                                  {meals.lunch > 0 && <span className="meal-type">🍽️ {meals.lunch}</span>}
                                  {meals.dinner > 0 && <span className="meal-type">🍖 {meals.dinner}</span>}
                                  {meals.snack > 0 && <span className="meal-type">🍿 {meals.snack}</span>}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {savePlanPreview.daysWithMeals === 0 && (
                        <div className="no-meals-warning">
                          <FaExclamationTriangle />
                          <p>No meals found in the selected date range. Please select dates that include planned meals.</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="modal-footer">
              <button
                type="button"
                className="btn-cancel"
                onClick={closeSaveMealPlanModal}
                disabled={savePlanLoading || saveToFavoritesLoading}
              >
                Cancel
              </button>

              <div className="action-buttons">
                <button
                  type="button"
                  className="btn-favorites"
                  onClick={saveToFavoritesFromModal}
                  disabled={
                    savePlanLoading ||
                    saveToFavoritesLoading ||
                    !savePlanName.trim() ||
                    !savePlanStartDate ||
                    !savePlanEndDate ||
                    !savePlanPreview ||
                    savePlanPreview.totalMeals === 0
                  }
                  title="Save directly to favorites without backend storage"
                >
                  {saveToFavoritesLoading ? (
                    <>
                      <div className="circle-loader small white"></div>
                      Adding...
                    </>
                  ) : (
                    <>
                      <FaHeart /> Save to Favorites
                    </>
                  )}
                </button>

                <button
                  type="button"
                  className="btn-save"
                  onClick={saveMealPlanFromModal}
                  disabled={
                    savePlanLoading ||
                    saveToFavoritesLoading ||
                    !savePlanName.trim() ||
                    !savePlanStartDate ||
                    !savePlanEndDate ||
                    !savePlanPreview ||
                    savePlanPreview.totalMeals === 0
                  }
                  title="Save to backend and add to favorites"
                >
                  {savePlanLoading ? (
                    <>
                      <div className="circle-loader small white"></div>
                      Saving...
                    </>
                  ) : (
                    <>
                      <FaSave /> Save Meal Plan
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
};

export default MealPlan;
