import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import EmailValidator from '../EmailValidator/EmailValidator';
import PasswordStrengthIndicator from '../PasswordStrengthIndicator/PasswordStrengthIndicator';
// TermsModal removed - will be handled during first login
import analyticsService from '../../services/analyticsService';
import '../../../src/styles/Auth.css';

function SignUp() {
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    password: '',
    confirmPassword: '',
    dob: '',
    gender: '',
    barangay: ''
  });

  const [errors, setErrors] = useState({});
  const [passwordStrength, setPasswordStrength] = useState({
    score: 0,
    label: '',
    className: ''
  });
  const [showEmailValidation, setShowEmailValidation] = useState(false);
  const [showPasswordStrength, setShowPasswordStrength] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [apiError, setApiError] = useState('');
  // Terms will be handled during first login, not registration
  const navigate = useNavigate();

  // Check if user is already logged in
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      navigate('/home');
    }
  }, [navigate]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Clear API error when user makes changes
    if (apiError) {
      setApiError('');
    }

    // Real-time validation
    validateFieldRealTime(name, value);

    // Show email validation when user starts typing
    if (name === 'email') {
      setShowEmailValidation(value.length > 0);
    }

    // Show password strength when user starts typing
    if (name === 'password') {
      setShowPasswordStrength(value.length > 0);
      checkPasswordStrength(value);
    }
  };

  const validateFieldRealTime = (fieldName, value) => {
    const newErrors = { ...errors };

    switch (fieldName) {
      case 'fullName':
        if (!value.trim()) {
          newErrors.fullName = 'Full name is required';
        } else if (value.trim().length < 2) {
          newErrors.fullName = 'Full name must be at least 2 characters';
        } else {
          delete newErrors.fullName;
        }
        break;

      case 'email':
        if (!value) {
          newErrors.email = 'Email is required';
        } else if (!/^[^\s@]+@gmail\.com$/i.test(value)) {
          newErrors.email = 'Email must be gmail';
        } else {
          delete newErrors.email;
        }
        break;

      case 'password':
        if (!value) {
          newErrors.password = 'Password is required';
        } else if (value.length < 5) {
          newErrors.password = 'Password must be at least 5 characters';
        } else {
          delete newErrors.password;
        }
        break;

      case 'confirmPassword':
        if (!value) {
          newErrors.confirmPassword = 'Please confirm your password';
        } else if (formData.password !== value) {
          newErrors.confirmPassword = 'Passwords do not match';
        } else {
          delete newErrors.confirmPassword;
        }
        break;

      case 'dob':
        if (!value) {
          newErrors.dob = 'Date of birth is required';
        } else {
          const dobDate = new Date(value);
          const today = new Date();
          const minAgeDate = new Date();
          minAgeDate.setFullYear(today.getFullYear() - 13);

          if (dobDate > today) {
            newErrors.dob = 'Date of birth cannot be in the future';
          } else if (dobDate > minAgeDate) {
            newErrors.dob = 'You must be at least 13 years old to register';
          } else {
            delete newErrors.dob;
          }
        }
        break;

      case 'gender':
        if (!value) {
          newErrors.gender = 'Gender is required';
        } else {
          delete newErrors.gender;
        }
        break;

      case 'barangay':
        if (!value) {
          newErrors.barangay = 'Barangay is required';
        } else {
          delete newErrors.barangay;
        }
        break;

      default:
        break;
    }

    setErrors(newErrors);
  };

  const checkPasswordStrength = (password) => {
    // Simple password strength check
    if (!password) {
      setPasswordStrength({
        score: 0,
        label: '',
        className: ''
      });
      return;
    }
    
    let score = 0;
    
    // Length check
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;
    
    // Complexity checks
    if (/[A-Z]/.test(password)) score += 1;
    if (/[0-9]/.test(password)) score += 1;
    if (/[^A-Za-z0-9]/.test(password)) score += 1;
    
    let strengthLabel = '';
    let strengthClass = '';
    
    if (score <= 1) {
      strengthLabel = 'Weak';
      strengthClass = 'weak';
    } else if (score <= 3) {
      strengthLabel = 'Moderate';
      strengthClass = 'moderate';
    } else {
      strengthLabel = 'Strong';
      strengthClass = 'strong';
    }
    
    setPasswordStrength({
      score,
      label: strengthLabel,
      className: strengthClass
    });
  };

const validateForm = () => {
  const newErrors = {};

  // Full Name validation
  if (!formData.fullName.trim()) {
    newErrors.fullName = 'Full name is required';
  } else if (formData.fullName.trim().length < 2) {
    newErrors.fullName = 'Full name must be at least 2 characters';
  }

  // Email validation - Gmail only
  if (!formData.email) {
    newErrors.email = 'Email is required';
  } else if (!/^[^\s@]+@gmail\.com$/i.test(formData.email)) {
    newErrors.email = 'Email must be gmail';
  }

  // Password validation
  if (!formData.password) {
    newErrors.password = 'Password is required';
  } else if (formData.password.length < 5) {
    newErrors.password = 'Password must be at least 5 characters';
  }

  // Confirm Password validation
  if (!formData.confirmPassword) {
    newErrors.confirmPassword = 'Please confirm your password';
  } else if (formData.password !== formData.confirmPassword) {
    newErrors.confirmPassword = 'Passwords do not match';
  }

  // Date of Birth validation
  if (!formData.dob) {
    newErrors.dob = 'Date of birth is required';
  } else {
    const dobDate = new Date(formData.dob);
    const today = new Date();
    const minAgeDate = new Date();
    minAgeDate.setFullYear(today.getFullYear() - 13); // Minimum age of 13

    if (dobDate > today) {
      newErrors.dob = 'Date of birth cannot be in the future';
    } else if (dobDate > minAgeDate) {
      newErrors.dob = 'You must be at least 13 years old to register';
    }
  }

  // Gender validation
  if (!formData.gender) {
    newErrors.gender = 'Gender is required';
  }

  // Barangay validation
  if (!formData.barangay) {
    newErrors.barangay = 'Barangay is required';
  }

  setErrors(newErrors);
  return Object.keys(newErrors).length === 0;
};

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    setApiError('');
    
    try {
      // Split fullName into firstName and lastName
      const nameParts = formData.fullName.trim().split(' ');
      const firstName = nameParts[0] || 'User';
      const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : 'Account';

      // Create username from fullName (lowercase, no spaces)
      const username = formData.fullName.replace(/\s+/g, '').toLowerCase() || 'user_' + Date.now();

      // Create the data object to send to the backend
      const userData = {
        username: username,
        email: formData.email,
        password: formData.password,
        firstName: firstName,
        lastName: lastName,
        dateOfBirth: formData.dob,
        gender: formData.gender || 'prefer-not-to-say',
        barangay: formData.barangay
      };

      // Register the user directly (terms will be handled during first login)
      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const response = await axios.post(`${API_BASE_URL}/users/register`, userData);

      // Check if email verification is required
      if (response.data.requiresVerification) {
        // Navigate to OTP verification page
        navigate('/otp-verification', {
          state: { email: formData.email.trim().toLowerCase() }
        });
      } else {
        // Store the JWT token in localStorage (for cases where verification is not required)
        localStorage.setItem('token', response.data.token);

        // Store user info (don't store sensitive data)
        if (response.data.user) {
          localStorage.setItem('user', JSON.stringify({
            id: response.data.user.id,
            email: response.data.user.email,
            username: response.data.user.username
          }));
        }

        // Enable analytics tracking after successful registration
        analyticsService.enable();

        // Redirect to home page after successful registration
        navigate("/home");
      }
    } catch (err) {
      if (err.response) {
        // Check if user already exists
        if (err.response.status === 409 && err.response.data.userExists) {
          setApiError(err.response.data.message || 'An account with this email already exists. Please try logging in instead.');
        } else {
          setApiError(err.response.data.message || 'Registration failed. Please try again.');
        }
      } else if (err.request) {
        setApiError('Server not responding. Please try again later.');
      } else {
        setApiError('An unexpected error occurred. Please try again.');
      }
      console.error('Registration error:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Terms acceptance handlers removed - will be handled during first login

  return (
    <div className="auth-container">
      <div className="auth-box">
        <div className="heading">Create Account</div>
        
        {apiError && <div className="error-message">{apiError}</div>}
        
        <form className="form" onSubmit={handleSubmit}>
          <div className="form-grid">
            {/* Full Name */}
            <div className="input-group full-width">
              <label htmlFor="fullName">Full Name *</label>
              <input
                placeholder="Enter your full name"
                id="fullName"
                name="fullName"
                type="text"
                className={`input ${errors.fullName ? 'error' : ''}`}
                value={formData.fullName}
                onChange={handleChange}
              />
              {errors.fullName && <div className="validation-message">{errors.fullName}</div>}
            </div>

            {/* Email */}
            <div className="input-group full-width">
              <label htmlFor="email">Email Address *</label>
              <input
                placeholder="Enter your email address"
                id="email"
                name="email"
                type="email"
                className={`input ${errors.email ? 'error' : ''}`}
                value={formData.email}
                onChange={handleChange}
              />
              {errors.email && <div className="validation-message">{errors.email}</div>}
              <EmailValidator
                email={formData.email}
                showValidation={showEmailValidation}
              />
            </div>
            
            {/* Password */}
            <div className="input-group full-width">
              <label htmlFor="password">Password *</label>
              <input
                placeholder="Create a password"
                id="password"
                name="password"
                type="password"
                className={`input ${errors.password ? 'error' : ''}`}
                value={formData.password}
                onChange={handleChange}
              />
              {errors.password && <div className="validation-message">{errors.password}</div>}
              <PasswordStrengthIndicator
                password={formData.password}
              />
            </div>

            {/* Confirm Password */}
            <div className="input-group full-width">
              <label htmlFor="confirmPassword">Confirm Password *</label>
              <input
                placeholder="Confirm your password"
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                className={`input ${errors.confirmPassword ? 'error' : ''}`}
                value={formData.confirmPassword}
                onChange={handleChange}
              />
              {errors.confirmPassword && <div className="validation-message">{errors.confirmPassword}</div>}
            </div>

            {/* Date of Birth */}
            <div className="input-group">
              <label htmlFor="dob">Date of Birth *</label>
              <input
                id="dob"
                name="dob"
                type="date"
                className={`input ${errors.dob ? 'error' : ''}`}
                value={formData.dob}
                onChange={handleChange}
              />
              {errors.dob && <div className="validation-message">{errors.dob}</div>}
            </div>

            {/* Gender */}
            <div className="input-group">
              <label htmlFor="gender">Gender *</label>
              <select
                id="gender"
                name="gender"
                className={`input ${errors.gender ? 'error' : ''}`}
                value={formData.gender}
                onChange={handleChange}
              >
                <option value="" disabled>Select Gender</option>
                <option value="male">Male</option>
                <option value="female">Female</option>
                <option value="other">Other</option>
                <option value="prefer-not-to-say">Prefer not to say</option>
              </select>
              {errors.gender && <div className="validation-message">{errors.gender}</div>}
            </div>
            
            {/* Barangay */}
            <div className="input-group full-width">
              <label htmlFor="barangay">Barangay *</label>
              <input
                list="barangay-options"
                placeholder="Select your barangay"
                name="barangay"
                id="barangay"
                className={`input ${errors.barangay ? 'error' : ''}`}
                value={formData.barangay}
                onChange={handleChange}
              />
              <datalist id="barangay-options">
                <option value="Barangay Aguho" />
                <option value="Barangay Magtanggol" />
                <option value="Barangay Martires Del 96" />
                <option value="Barangay Poblacion" />
                <option value="Barangay San Pedro" />
                <option value="Barangay San Roque" />
                <option value="Barangay Santa Ana" />
                <option value="Barangay Santo Rosario-Kanluran" />
                <option value="Barangay Santo Rosario-Silangan" />
                <option value="Barangay Tabacalera" />
              </datalist>
              {errors.barangay && <div className="validation-message">{errors.barangay}</div>}
            </div>
          </div>
          
          {/* Submit Button */}
          <button 
            type="submit"
            className="auth-button"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Creating Account...' : 'Create Account'}
          </button>
        </form>
        
        {/* Already have an account? Link */}
        <div className="auth-link">
          <span>Already have an account? </span>
          <Link to="/login">Sign In</Link>
        </div>
      </div>

      {/* Terms modal removed - will be handled during first login */}
    </div>
  );
}

export default SignUp;
