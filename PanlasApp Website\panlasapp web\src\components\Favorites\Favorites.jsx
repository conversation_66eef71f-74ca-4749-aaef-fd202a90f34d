import React, { useState } from "react";
import Header from "../Header/Header";
import Sidebar from "../Sidebar/Sidebar";
import { FaHeart, FaTimes, FaTrash, FaCalendarAlt, FaUtensils, FaClock, FaPlus } from "react-icons/fa";
import "../../../src/App.css";
import { useFavorites } from "../../components/Favorites/FavoritesContext";

// Dietary badge mapping function (same as Home.jsx)
function getDietaryBadges(meal) {
  const BADGE_MAP = [
    { key: 'Vegan', label: 'Vegan', color: '#4CAF50', icon: '🌱' },
    { key: 'Vegetarian', label: 'Vegetarian', color: '#8BC34A', icon: '🥬' },
    { key: 'Flexitarian', label: 'Flexitarian', color: '#A3C9A8', icon: '🤝' },
    { key: 'Dairy-Free', label: 'Dairy-Free', color: '#00BCD4', icon: '🥛🚫' },
    { key: 'Egg-Free', label: 'Egg-Free', color: '#FFEB3B', icon: '🥚🚫' },
    { key: 'Gluten-Free', label: 'Gluten-Free', color: '#FF9800', icon: '🌾' },
    { key: 'Soy-Free', label: 'Soy-Free', color: '#9E9E9E', icon: '🌱🚫' },
    { key: 'Nut-Free', label: 'Nut-Free', color: '#795548', icon: '🥜🚫' },
    { key: 'Low-Carb', label: 'Low-Carb', color: '#9C27B0', icon: '🥩' },
    { key: 'Low-Sugar', label: 'Low-Sugar', color: '#607D8B', icon: '🍬⬇️' },
    { key: 'Sugar-Free', label: 'Sugar-Free', color: '#607D8B', icon: '🍬🚫' },
    { key: 'Low-Fat', label: 'Low-Fat', color: '#03A9F4', icon: '🥗' },
    { key: 'Low-Sodium', label: 'Low-Sodium', color: '#B0BEC5', icon: '🧂⬇️' },
    { key: 'Organic', label: 'Organic', color: '#388E3C', icon: '🍃' },
    { key: 'Halal', label: 'Halal', color: '#2196F3', icon: '☪️' },
    { key: 'High-Protein', label: 'High-Protein', color: '#E91E63', icon: '💪' },
    { key: 'Pescatarian', label: 'Pescatarian', color: '#00B8D4', icon: '🐟' },
    { key: 'Keto', label: 'Keto', color: '#FFB300', icon: '🥓' },
    { key: 'Plant-Based', label: 'Plant-Based', color: '#43A047', icon: '🌿' },
    { key: 'Kosher', label: 'Kosher', color: '#3F51B5', icon: '✡️' },
    { key: 'Climatarian', label: 'Climatarian', color: '#689F38', icon: '🌎' },
    { key: 'Raw Food', label: 'Raw Food', color: '#AED581', icon: '🥗' },
    { key: 'Mediterranean', label: 'Mediterranean', color: '#00ACC1', icon: '🌊' },
    { key: 'Paleo', label: 'Paleo', color: '#A1887F', icon: '🍖' },
    { key: 'Kangatarian', label: 'Kangatarian', color: '#D84315', icon: '🦘' },
    { key: 'Pollotarian', label: 'Pollotarian', color: '#FBC02D', icon: '🍗' },
  ];
  const tags = meal.dietaryTags || [];
  return BADGE_MAP.filter(badge =>
    tags.some(tag => tag.toLowerCase() === badge.key.toLowerCase())
  );
}

const Favorites = () => {
  const [sidebarActive, setSidebarActive] = useState(false);
  const [selectedMeal, setSelectedMeal] = useState(null);
  const [selectedMealPlan, setSelectedMealPlan] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [showMealPlanModal, setShowMealPlanModal] = useState(false);
  const [activeTab, setActiveTab] = useState('meals'); // 'meals' or 'mealplans'
  const [notification, setNotification] = useState({ show: false, message: "" });
  const [loadingMealPlanDetails, setLoadingMealPlanDetails] = useState(false);
  const [mealPlanDetails, setMealPlanDetails] = useState(null);

  const { favorites, favoriteMealPlans, removeFavorite, removeFavoriteMealPlan } = useFavorites();

  const toggleSidebar = () => {
    setSidebarActive(!sidebarActive);
  };

  const handleRemoveFavorite = (e, dishId) => {
    e.stopPropagation();
    removeFavorite(dishId);
    showNotification("Removed from favorites");
  };

  const handleRemoveFavoriteMealPlan = async (e, favPlan) => {
    e.stopPropagation();
    const favoriteId = favPlan._id || favPlan.id;
    const result = await removeFavoriteMealPlan(favoriteId);
    if (result.success) {
      showNotification("Meal plan removed from favorites");
    } else {
      showNotification("Failed to remove meal plan from favorites");
    }
  };

  const showNotification = (message) => {
    setNotification({ show: true, message });
    setTimeout(() => {
      setNotification({ show: false, message: "" });
    }, 3000);
  };

  const openMealDetails = (dish) => {
    setSelectedMeal(dish);
    setShowModal(true);
  };

  const closeMealDetails = () => {
    setShowModal(false);
  };

  const fetchMealPlanDetails = async (mealPlanId) => {
    try {
      setLoadingMealPlanDetails(true);
      const token = localStorage.getItem("token");
      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const singlePlanResponse = await fetch(`${API_BASE_URL}/meal-plans/saved/${mealPlanId}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      if (!singlePlanResponse.ok) {
        return null;
      }
      const singlePlan = await singlePlanResponse.json();
      if (singlePlan.templateName) {
        const allPlansResponse = await fetch(`${API_BASE_URL}/meal-plans`, {
          headers: { Authorization: `Bearer ${token}` },
        });
        if (allPlansResponse.ok) {
          const allPlans = await allPlansResponse.json();
          const relatedPlans = allPlans.filter(plan =>
            plan.templateName === singlePlan.templateName
          );
          const allMeals = [];
          relatedPlans.forEach(plan => {
            const mealTypes = ['breakfast', 'lunch', 'dinner'];
            mealTypes.forEach(mealType => {
              if (plan[mealType] && plan[mealType].length > 0) {
                plan[mealType].forEach(meal => {
                  allMeals.push({
                    date: plan.date,
                    mealType: mealType,
                    mealData: {
                      name: meal.name,
                      calories: meal.calories,
                      protein: meal.protein,
                      carbs: meal.carbs,
                      fat: meal.fat,
                      image: meal.image,
                      description: meal.description,
                      ingredients: meal.ingredients,
                      instructions: meal.instructions,
                      category: meal.category,
                      dietaryTags: meal.dietaryTags
                    }
                  });
                });
              }
            });
          });
          return {
            ...singlePlan,
            meals: allMeals,
            relatedPlans: relatedPlans
          };
        }
      }
      return singlePlan;
    } catch (error) {
      console.error('Error fetching meal plan details:', error);
      return null;
    } finally {
      setLoadingMealPlanDetails(false);
    }
  };

  const openMealPlanDetails = async (mealPlan) => {
    setSelectedMealPlan(mealPlan);
    setShowMealPlanModal(true);
    if (mealPlan.plan) {
      const planId = typeof mealPlan.plan === 'string' ? mealPlan.plan : mealPlan.plan._id;
      const detailedPlan = await fetchMealPlanDetails(planId);
      if (detailedPlan) {
        setMealPlanDetails(detailedPlan);
      }
    }
  };

  const closeMealPlanDetails = () => {
    setShowMealPlanModal(false);
    setMealPlanDetails(null);
    setSelectedMealPlan(null);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="app-container">
      <Header toggleSidebar={toggleSidebar} />
      <Sidebar isActive={sidebarActive} />
      <div className="content-area">
        <div className="main-content">
            <h1>FAVORITES</h1>

            {/* Notification */}
            {notification.show && (
              <div className="notification">{notification.message}</div>
            )}

            {/* Tabs */}
            <div className="favorites-tabs">
              <button
                className={`tab-btn ${activeTab === 'meals' ? 'active' : ''}`}
                onClick={() => setActiveTab('meals')}
              >
                <FaUtensils /> Favorite Meals ({favorites?.length || 0})
              </button>
              <button
                className={`tab-btn ${activeTab === 'mealplans' ? 'active' : ''}`}
                onClick={() => setActiveTab('mealplans')}
              >
                <FaCalendarAlt /> Favorite Meal Plans ({favoriteMealPlans?.length || 0})
              </button>
            </div>

            {/* Meals Tab Content */}
            {activeTab === 'meals' && (
              <>
                {favorites && favorites.length > 0 ? (
                  <div className="food-grid">
                    {favorites.map((dish) => (
                      <div key={dish.id || dish._id} className="food-card">
                        <div className="food-card-image">
                          <img src={dish.image} alt={dish.name} />
                          <button
                            className="favorite-btn"
                            onClick={(e) =>
                              handleRemoveFavorite(e, dish.id || dish._id)
                            }
                            title="Remove from favorites"
                          >
                            <FaTrash className="trash-icon" />
                          </button>
                        </div>
                        <div className="food-card-content">
                          {/* 1. Meal Name */}
                          <h3 className="food-card-title">{dish.name}</h3>
                          {/* 2. Meta row: Calories, Prep Time, Rating */}
                          <div className="food-card-meta">
                            {dish.calories && (
                              <div className="meta-item calories-tag">
                                <span>{dish.calories} cal</span>
                              </div>
                            )}
                            {dish.prepTime && (
                              <div className="meta-item prep-time-tag">
                                <FaClock /> <span>{dish.prepTime} min</span>
                              </div>
                            )}
                            <div className="meta-item rating">
                              <span>{dish.rating} &#9733;</span>
                            </div>
                          </div>
                          {/* 3. Dietary Tags */}
                          {getDietaryBadges(dish).length > 0 && (
                            <div className="food-card-tags">
                              {getDietaryBadges(dish).map((badge, idx) => (
                                <span
                                  key={idx}
                                  className="dietary-tag"
                                  style={{
                                    background: badge.color,
                                    color: '#fff',
                                    display: 'inline-flex',
                                    alignItems: 'center',
                                    gap: '0.25em',
                                  }}
                                  title={badge.label}
                                >
                                  <span className="dietary-tag-icon">{badge.icon}</span>
                                  <span className="dietary-tag-label">{badge.label}</span>
                                </span>
                              ))}
                            </div>
                          )}
                          {/* 4. Description */}
                          {dish.description && (
                            <div className="food-card-description">
                              <span>
                                {dish.description.length > 80
                                  ? dish.description.slice(0, 80) + "..."
                                  : dish.description}
                              </span>
                            </div>
                          )}
                          <button
                            className="view-meal-btn"
                            onClick={() => openMealDetails(dish)}
                          >
                            View Meal
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="no-favorites">
                    <h2>You haven't added any favorite meals yet</h2>
                    <p>
                      Click the heart icon on meals you like to add them to your
                      favorites
                    </p>
                  </div>
                )}
              </>
            )}

            {/* Meal Plans Tab Content */}
            {activeTab === 'mealplans' && (
              <>
                {favoriteMealPlans && favoriteMealPlans.length > 0 ? (
                  <div className="meal-plans-grid">
                    {favoriteMealPlans.map((favPlan) => {
                      const plan = favPlan.plan || favPlan;
                      const planName = favPlan.name || plan.templateName || plan.name || 'Unnamed Meal Plan';

                      // Handle different date formats - new format has startDate/endDate, old format has date
                      let planDate = favPlan.date || plan.date;
                      if (!planDate && favPlan.startDate) {
                        // For new format, show date range or just start date
                        if (favPlan.endDate && favPlan.startDate !== favPlan.endDate) {
                          planDate = `${favPlan.startDate} to ${favPlan.endDate}`;
                        } else {
                          planDate = favPlan.startDate;
                        }
                      }

                      // Get meal types from the meals data if not directly available
                      let mealTypes = favPlan.mealTypes || plan.mealTypes;
                      if (!mealTypes && favPlan.meals && favPlan.meals.length > 0) {
                        mealTypes = [...new Set(favPlan.meals.map(meal => meal.mealType))];
                      }

                      return (
                        <div key={favPlan._id || plan._id || plan.id} className="meal-plan-card">
                          <div className="meal-plan-header">
                            <h3>{planName}</h3>
                            <button
                              className="favorite-btn"
                              onClick={(e) =>
                                handleRemoveFavoriteMealPlan(e, favPlan)
                              }
                              title="Remove from favorites"
                            >
                              <FaTrash className="trash-icon" />
                            </button>
                          </div>
                          <div className="meal-plan-content">
                            <div className="meal-plan-meta">
                              <div className="meta-item">
                                <FaCalendarAlt />
                                <span>{planDate ? (planDate.includes(' to ') ? planDate : formatDate(planDate)) : 'Invalid Date'}</span>
                              </div>
                              <div className="meta-item">
                                <FaUtensils />
                                <span>{favPlan.totalMeals || plan.totalMeals || 0} meals planned</span>
                              </div>
                              {mealTypes && mealTypes.length > 0 && (
                                <div className="meta-item">
                                  <span className="dietary-preference">{mealTypes.join(', ')}</span>
                                </div>
                              )}
                            </div>
                            <button
                              className="view-meal-plan-btn"
                              onClick={() => openMealPlanDetails(favPlan)}
                            >
                              View Meal Plan
                            </button>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="no-favorites">
                    <h2>You haven't added any favorite meal plans yet</h2>
                    <p>
                      Create meal plans and add them to favorites to see them here
                    </p>
                  </div>
                )}
              </>
            )}

            {/* Meal Details Modal */}
            {showModal && selectedMeal && (
              <div className="modal-overlay">
                <div className="modal-content">
                  <div className="modal-header">
                    <h2>{selectedMeal.name}</h2>
                    <button className="close-modal" onClick={closeMealDetails}>
                      <FaTimes />
                    </button>
                  </div>
                  <div className="modal-body">
                    <div className="meal-image">
                      <img src={selectedMeal.image} alt={selectedMeal.name} />
                    </div>
                    <div className="meal-details">
                      <p className="meal-description">
                        {selectedMeal.description}
                      </p>
                      <div className="meal-meta">
                        <span className="meal-rating">
                          {selectedMeal.rating} &#9733;
                        </span>
                        <span className="meal-category">
                          {selectedMeal.category}
                        </span>
                        <span className="meal-price">
                          Calories: {selectedMeal.calories} (
                          {selectedMeal.priceRange} Range)
                        </span>
                      </div>
                      {selectedMeal.ingredients && (
                        <div className="meal-ingredients">
                          <h3>Ingredients</h3>
                          <ul>
                            {selectedMeal.ingredients.map((ingredient, idx) => (
                              <li key={idx}>{ingredient}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                      {selectedMeal.steps && (
                        <div className="meal-steps">
                          <h3>Cooking Steps</h3>
                          <ol>
                            {selectedMeal.steps.map((step, idx) => (
                              <li key={idx}>{step}</li>
                            ))}
                          </ol>
                        </div>
                      )}
                      {selectedMeal.dietaryTags && (
                        <div className="meal-tags">
                          <h3>Dietary Tags</h3>
                          <div className="tags-container">
                            {selectedMeal.dietaryTags.map((tag, idx) => (
                              <span key={idx} className="dietary-tag">
                                {tag}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                      {selectedMeal.mealType && (
                        <div className="meal-types">
                          <h3>Meal Types</h3>
                          <div className="
                          tags-container">
                            {selectedMeal.mealType.map((type, idx) => (
                              <span key={idx} className="meal-type-tag">
                                {type}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Meal Plan Details Modal */}
            {showMealPlanModal && selectedMealPlan && (
              <div className="modal-overlay">
                <div className="modal-content meal-plan-modal">
                  <div className="modal-header">
                    <h2>{selectedMealPlan.name || selectedMealPlan.templateName || 'Meal Plan'}</h2>
                    <button className="close-modal" onClick={closeMealPlanDetails}>
                      <FaTimes />
                    </button>
                  </div>
                  <div className="modal-body">
                    <div className="meal-plan-details">
                      <div className="plan-meta">
                        <div className="meta-row">
                          <FaCalendarAlt />
                          <span>Date: {(() => {
                            let planDate = selectedMealPlan.date;
                            if (!planDate && selectedMealPlan.startDate) {
                              if (selectedMealPlan.endDate && selectedMealPlan.startDate !== selectedMealPlan.endDate) {
                                planDate = `${selectedMealPlan.startDate} to ${selectedMealPlan.endDate}`;
                              } else {
                                planDate = selectedMealPlan.startDate;
                              }
                            }
                            return planDate ? (planDate.includes(' to ') ? planDate : formatDate(planDate)) : 'Invalid Date';
                          })()}</span>
                        </div>
                        <div className="meta-row">
                          <FaUtensils />
                          <span>Total Meals: {selectedMealPlan.totalMeals || 0}</span>
                        </div>
                        {selectedMealPlan.totalCalories && (
                          <div className="meta-row">
                            <span>Total Calories: {selectedMealPlan.totalCalories}</span>
                          </div>
                        )}
                        {selectedMealPlan.mealTypes && selectedMealPlan.mealTypes.length > 0 && (
                          <div className="meta-row">
                            <span>Meal Types: {selectedMealPlan.mealTypes.join(', ')}</span>
                          </div>
                        )}
                      </div>

                      {/* Display detailed meals if available */}
                      {loadingMealPlanDetails && (
                        <div className="loading-meals">
                          <p>Loading meal details...</p>
                        </div>
                      )}

                      {mealPlanDetails && mealPlanDetails.meals && mealPlanDetails.meals.length > 0 && (
                        <div className="planned-meals">
                          <h3>Planned Meals</h3>
                          <div className="meals-grid-container">
                            {Object.entries(
                              mealPlanDetails.meals.reduce((acc, meal) => {
                                const date = meal.date;
                                if (!acc[date]) acc[date] = {};
                                if (!acc[date][meal.mealType]) acc[date][meal.mealType] = [];
                                acc[date][meal.mealType].push(meal);
                                return acc;
                              }, {})
                            )
                            .sort(([dateA], [dateB]) => new Date(dateA) - new Date(dateB))
                            .map(([date, mealsByType], dayIndex) => (
                              <div key={date} className="day-card-grid">
                                <div className="day-header-grid">
                                  <h4>Day {dayIndex + 1}</h4>
                                  <span className="day-date">{formatDate(date)}</span>
                                </div>
                                <div className="day-meals-grid">
                                  {Object.entries(mealsByType)
                                    .sort(([mealTypeA], [mealTypeB]) => {
                                      const order = { breakfast: 0, lunch: 1, dinner: 2 };
                                      return (order[mealTypeA] || 999) - (order[mealTypeB] || 999);
                                    })
                                    .map(([mealType, meals]) => (
                                      <div key={mealType} className="meal-type-card">
                                        <div className="meal-type-header">
                                          <FaClock className="meal-time-icon" />
                                          <span className="meal-type-name">
                                            {mealType.charAt(0).toUpperCase() + mealType.slice(1)}
                                          </span>
                                        </div>
                                        <div className="meal-items-grid">
                                          {meals.map((meal, idx) => (
                                            <div key={idx} className="meal-item-card">
                                              <div className="meal-name">{meal.mealData?.name || 'Unknown Meal'}</div>
                                              {meal.mealData?.calories && (
                                                <div className="meal-calories">{meal.mealData.calories} cal</div>
                                              )}
                                              {meal.mealData?.category && (
                                                <div className="meal-category">{meal.mealData.category}</div>
                                              )}
                                            </div>
                                          ))}
                                        </div>
                                      </div>
                                    ))}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {!loadingMealPlanDetails && !mealPlanDetails && (
                        <div className="favorite-plan-summary">
                          <h3>Meal Plan Summary</h3>
                          <div className="summary-content">
                            <div className="summary-stats">
                              <div className="stat-card">
                                <div className="stat-icon">
                                  <FaUtensils />
                                </div>
                                <div className="stat-info">
                                  <span className="stat-value">{selectedMealPlan.totalMeals || 0}</span>
                                  <span className="stat-label">Total Meals</span>
                                </div>
                              </div>

                              {selectedMealPlan.totalCalories && selectedMealPlan.totalCalories > 0 && (
                                <div className="stat-card">
                                  <div className="stat-icon">
                                    <span className="calorie-icon">🔥</span>
                                  </div>
                                  <div className="stat-info">
                                    <span className="stat-value">{selectedMealPlan.totalCalories}</span>
                                    <span className="stat-label">Total Calories</span>
                                  </div>
                                </div>
                              )}

                              <div className="stat-card">
                                <div className="stat-icon">
                                  <FaCalendarAlt />
                                </div>
                                <div className="stat-info">
                                  <span className="stat-value">{(() => {
                                    let planDate = selectedMealPlan.date;
                                    if (!planDate && selectedMealPlan.startDate) {
                                      if (selectedMealPlan.endDate && selectedMealPlan.startDate !== selectedMealPlan.endDate) {
                                        planDate = `${selectedMealPlan.startDate} to ${selectedMealPlan.endDate}`;
                                      } else {
                                        planDate = selectedMealPlan.startDate;
                                      }
                                    }
                                    return planDate ? (planDate.includes(' to ') ? planDate : formatDate(planDate)) : 'Invalid Date';
                                  })()}</span>
                                  <span className="stat-label">Plan Date</span>
                                </div>
                              </div>
                            </div>

                            {selectedMealPlan.mealTypes && selectedMealPlan.mealTypes.length > 0 && (
                              <div className="meal-types-section">
                                <h4>Included Meal Types</h4>
                                <div className="meal-types-list">
                                  {selectedMealPlan.mealTypes.map((type, index) => (
                                    <div key={index} className="meal-type-badge">
                                      <span className="meal-type-icon">
                                        {type === 'breakfast' && '🌅'}
                                        {type === 'lunch' && '☀️'}
                                        {type === 'dinner' && '🌙'}
                                        {type === 'snack' && '🍎'}
                                      </span>
                                      <span className="meal-type-name">
                                        {type.charAt(0).toUpperCase() + type.slice(1)}
                                      </span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}

                            <div className="plan-actions">
                              <div className="action-note">
                                <p><strong>Note:</strong> This is a saved favorite meal plan template. To view detailed meal information, you can recreate this plan in your meal planner.</p>
                              </div>
                              <button
                                className="recreate-plan-btn"
                                onClick={() => {
                                  closeMealPlanDetails();
                                  // Navigate to meal planner - you might need to implement this navigation
                                  window.location.href = '/meal-planner';
                                }}
                              >
                                <FaPlus />
                                Recreate This Plan
                              </button>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}


        </div>
      </div>
    </div>
  );
};

export default Favorites;
