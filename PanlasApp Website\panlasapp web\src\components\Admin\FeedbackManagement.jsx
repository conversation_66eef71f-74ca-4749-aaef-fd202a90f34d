import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import './FeedbackManagement.css';
import Layout from '../Layout/Layout';

const FeedbackManagement = () => {
  const [feedback, setFeedback] = useState([]);
  const [feedbackStats, setFeedbackStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedFeedback, setSelectedFeedback] = useState(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showResponseModal, setShowResponseModal] = useState(false);
  const [responseMessage, setResponseMessage] = useState('');
  const [filters, setFilters] = useState({
    category: 'all',
    status: 'all',
    priority: 'all'
  });
  const navigate = useNavigate();

  useEffect(() => {
    fetchFeedbackData();
  }, [filters]);

  const fetchFeedbackData = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        navigate('/login');
        return;
      }

      const config = {
        headers: {
          'x-auth-token': token
        },
        params: filters
      };

      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const response = await axios.get(`${API_BASE_URL}/feedback/all`, config);
      setFeedback(response.data.feedback);
      setFeedbackStats(response.data.stats);
      setLoading(false);
    } catch (err) {
      if (err.response && err.response.status === 403) {
        setError('Access denied. Admin privileges required.');
        navigate('/home');
      } else {
        setError('Failed to load feedback data. Please try again.');
        console.error('Feedback fetch error:', err);
      }
      setLoading(false);
    }
  };

  const handleViewFeedback = (feedbackItem) => {
    setSelectedFeedback(feedbackItem);
    setShowDetailModal(true);
  };

  const handleRespondFeedback = (feedbackItem) => {
    setSelectedFeedback(feedbackItem);
    setResponseMessage('');
    setShowResponseModal(true);
  };

  const handleStatusUpdate = async (feedbackId, newStatus) => {
    try {
      const token = localStorage.getItem('token');
      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      await axios.put(
        `${API_BASE_URL}/feedback/${feedbackId}/status`,
        { status: newStatus },
        {
          headers: {
            'x-auth-token': token,
            'Content-Type': 'application/json'
          }
        }
      );
      
      // Update local state
      setFeedback(feedback.map(item => 
        item._id === feedbackId ? { ...item, status: newStatus } : item
      ));
      
      alert('Status updated successfully');
    } catch (error) {
      console.error('Error updating status:', error);
      alert('Failed to update status');
    }
  };

  const handleSubmitResponse = async () => {
    if (!responseMessage.trim()) {
      alert('Please enter a response message');
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      await axios.post(
        `${API_BASE_URL}/feedback/${selectedFeedback._id}/response`,
        { message: responseMessage },
        {
          headers: {
            'x-auth-token': token,
            'Content-Type': 'application/json'
          }
        }
      );

      // Update local state
      setFeedback(feedback.map(item => 
        item._id === selectedFeedback._id 
          ? { 
              ...item, 
              status: 'in_progress',
              adminResponse: {
                message: responseMessage,
                respondedAt: new Date()
              }
            } 
          : item
      ));

      setShowResponseModal(false);
      setResponseMessage('');
      alert('Response sent successfully');
    } catch (error) {
      console.error('Error sending response:', error);
      alert('Failed to send response');
    }
  };

  const handleFilterChange = (filterType, value) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  if (loading) return (
    <Layout>
      <div className="loading">Loading feedback data...</div>
    </Layout>
  );

  if (error) return (
    <Layout>
      <div className="error">{error}</div>
    </Layout>
  );

  return (
    <Layout>
      <div className="feedback-management">
      <div className="feedback-header">
        <h1>Feedback Management</h1>
        <button onClick={() => navigate('/admin')} className="back-button">
          ← Back to Admin Dashboard
        </button>
      </div>

      {/* Statistics */}
      {feedbackStats && (
        <div className="feedback-stats">
          <div className="stat-card">
            <h3>Total Feedback</h3>
            <p className="stat-number">{feedbackStats.total}</p>
          </div>
          <div className="stat-card">
            <h3>Open</h3>
            <p className="stat-number open">{feedbackStats.open}</p>
          </div>
          <div className="stat-card">
            <h3>In Progress</h3>
            <p className="stat-number progress">{feedbackStats.inProgress}</p>
          </div>
          <div className="stat-card">
            <h3>Resolved</h3>
            <p className="stat-number resolved">{feedbackStats.resolved}</p>
          </div>
          <div className="stat-card">
            <h3>Average Rating</h3>
            <p className="stat-number">{feedbackStats.avgRating ? feedbackStats.avgRating.toFixed(1) : 'N/A'}⭐</p>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="feedback-filters">
        <div className="filter-group">
          <label>Category:</label>
          <select 
            value={filters.category} 
            onChange={(e) => handleFilterChange('category', e.target.value)}
          >
            <option value="all">All Categories</option>
            <option value="bug_report">Bug Report</option>
            <option value="feature_request">Feature Request</option>
            <option value="general_feedback">General Feedback</option>
            <option value="user_experience">User Experience</option>
            <option value="meal_suggestions">Meal Suggestions</option>
            <option value="technical_issue">Technical Issue</option>
            <option value="other">Other</option>
          </select>
        </div>
        
        <div className="filter-group">
          <label>Status:</label>
          <select 
            value={filters.status} 
            onChange={(e) => handleFilterChange('status', e.target.value)}
          >
            <option value="all">All Status</option>
            <option value="open">Open</option>
            <option value="in_progress">In Progress</option>
            <option value="resolved">Resolved</option>
          </select>
        </div>
        
        <div className="filter-group">
          <label>Priority:</label>
          <select 
            value={filters.priority} 
            onChange={(e) => handleFilterChange('priority', e.target.value)}
          >
            <option value="all">All Priorities</option>
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
            <option value="urgent">Urgent</option>
          </select>
        </div>
      </div>

      {/* Feedback Table */}
      <div className="feedback-table-container">
        <table className="feedback-table">
          <thead>
            <tr>
              <th>User</th>
              <th>Subject</th>
              <th>Category</th>
              <th>Status</th>
              <th>Priority</th>
              <th>Rating</th>
              <th>Date</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {feedback.map((item) => (
              <tr key={item._id}>
                <td>{item.user ? item.user.username : 'Anonymous'}</td>
                <td title={item.subject}>
                  {item.subject.length > 40 ? `${item.subject.substring(0, 40)}...` : item.subject}
                </td>
                <td>
                  <span className={`category-tag ${item.category}`}>
                    {item.category.replace('_', ' ')}
                  </span>
                </td>
                <td>
                  <select 
                    value={item.status} 
                    onChange={(e) => handleStatusUpdate(item._id, e.target.value)}
                    className={`status-select ${item.status}`}
                  >
                    <option value="open">Open</option>
                    <option value="in_progress">In Progress</option>
                    <option value="resolved">Resolved</option>
                  </select>
                </td>
                <td>
                  <span className={`priority-tag ${item.priority}`}>
                    {item.priority}
                  </span>
                </td>
                <td>{item.rating ? `${item.rating}⭐` : 'N/A'}</td>
                <td>{new Date(item.createdAt).toLocaleDateString()}</td>
                <td>
                  <div className="action-buttons">
                    <button 
                      className="btn-view"
                      onClick={() => handleViewFeedback(item)}
                      title="View Details"
                    >
                      👁️
                    </button>
                    <button 
                      className="btn-respond"
                      onClick={() => handleRespondFeedback(item)}
                      title="Respond"
                    >
                      💬
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {feedback.length === 0 && (
          <div className="no-feedback">
            <p>No feedback submissions found.</p>
          </div>
        )}
      </div>

      {/* Detail Modal */}
      {showDetailModal && selectedFeedback && (
        <div className="modal-overlay" onClick={() => setShowDetailModal(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>Feedback Details</h2>
              <button onClick={() => setShowDetailModal(false)}>×</button>
            </div>
            <div className="modal-body">
              <div className="feedback-detail">
                <p><strong>User:</strong> {selectedFeedback.user ? selectedFeedback.user.username : 'Anonymous'}</p>
                <p><strong>Email:</strong> {selectedFeedback.user ? selectedFeedback.user.email : 'N/A'}</p>
                <p><strong>Subject:</strong> {selectedFeedback.subject}</p>
                <p><strong>Category:</strong> {selectedFeedback.category.replace('_', ' ')}</p>
                <p><strong>Status:</strong> {selectedFeedback.status.replace('_', ' ')}</p>
                <p><strong>Priority:</strong> {selectedFeedback.priority}</p>
                <p><strong>Rating:</strong> {selectedFeedback.rating ? `${selectedFeedback.rating}⭐` : 'N/A'}</p>
                <p><strong>Date:</strong> {new Date(selectedFeedback.createdAt).toLocaleString()}</p>
                <div className="message-section">
                  <strong>Message:</strong>
                  <div className="message-content">{selectedFeedback.message}</div>
                </div>
                {selectedFeedback.adminResponse && (
                  <div className="response-section">
                    <strong>Admin Response:</strong>
                    <div className="response-content">{selectedFeedback.adminResponse.message}</div>
                    <p className="response-date">
                      Responded on: {new Date(selectedFeedback.adminResponse.respondedAt).toLocaleString()}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Response Modal */}
      {showResponseModal && selectedFeedback && (
        <div className="modal-overlay" onClick={() => setShowResponseModal(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>Respond to Feedback</h2>
              <button onClick={() => setShowResponseModal(false)}>×</button>
            </div>
            <div className="modal-body">
              <div className="feedback-summary">
                <p><strong>User:</strong> {selectedFeedback.user ? selectedFeedback.user.username : 'Anonymous'}</p>
                <p><strong>Subject:</strong> {selectedFeedback.subject}</p>
                <div className="original-message">
                  <strong>Original Message:</strong>
                  <div className="message-content">{selectedFeedback.message}</div>
                </div>
              </div>
              <div className="response-form">
                <label htmlFor="responseMessage">Your Response:</label>
                <textarea
                  id="responseMessage"
                  value={responseMessage}
                  onChange={(e) => setResponseMessage(e.target.value)}
                  placeholder="Type your response here..."
                  rows={6}
                />
                <div className="modal-actions">
                  <button onClick={() => setShowResponseModal(false)} className="btn-cancel">
                    Cancel
                  </button>
                  <button onClick={handleSubmitResponse} className="btn-submit">
                    Send Response
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      </div>
    </Layout>
  );
};

export default FeedbackManagement;
